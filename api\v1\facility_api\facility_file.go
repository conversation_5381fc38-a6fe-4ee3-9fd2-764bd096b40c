package facility_api

import (
	"fmt"
	"mime/multipart"
	"net/http"

	v1 "github.com/Norray/medic-crew/api/v1"
	"github.com/Norray/medic-crew/resource"
	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp"
	"github.com/Norray/xrocket/xgorm"
	"github.com/Norray/xrocket/xtool"
	"github.com/gin-gonic/gin"
	"github.com/nicksnyder/go-i18n/v2/i18n"
)

type FacilityFileController struct {
	v1.CommonController
}

func NewFacilityFileController() FacilityFileController {
	return FacilityFileController{}
}

// @Tags Facility File
// @Summary 獲取機構文件圖片
// @Description 直接獲取機構文件的圖片內容，可以在瀏覽器中直接顯示
// @Router /v1/facility/facility-files/actions/preview [GET]
// @Security ApiKeyAuth
// @Param json query services.FacilityFileGetPreviewReq true "parameter"
// @Success 200 "Success"
func (con FacilityFileController) Preview(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.FacilityFileGetPreviewReq
	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		if !con.CheckCanAccess(nc, db, v1.ReqCommonCheck{FacilityFileId: req.FacilityFileId}) {
			nc.NoPermissionResponse(resource.ForbiddenMsg)
			return
		}

		// 獲取圖片數據
		resp, err := services.FacilityFileService.Preview(db, req)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}

		c.Writer.Header().Set("Content-Disposition", fmt.Sprintf("attachment; filename*=utf-8''%s", xtool.ReplacePlus(resp.Filename)))
		c.Data(http.StatusOK, services.FacilityFileService.GetFileMimeType(resp.Filename), resp.FileBytes)

	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Facility File
// @Summary 上載機構文件
// @Description
// @Router /v1/facility/facility-files/actions/upload [POST]
// @Produce json
// @Security ApiKeyAuth
// @Param json body services.FacilityFileUploadReq true "parameter"
// @Param file formData file true "文件"
// @Success 200 {object} services.FacilityFileUploadResp "Success"
func (con FacilityFileController) Upload(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.FacilityFileUploadReq
	if err := c.ShouldBind(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		if !con.CheckCanAccess(nc, db, v1.ReqCommonCheck{FacilityId: req.FacilityId}) {
			nc.NoPermissionResponse(resource.ForbiddenMsg)
			return
		}

		var checkMsg []string
		checker := xapp.NewCK(c)
		checker.
			Run(func() (bool, i18n.Message, error) {
				return services.FacilityFileService.CheckFileCodeExist(req.FileCode)
			})
		checkMsg, err = checker.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(checkMsg) > 0 {
			nc.BadRequestResponseWithCheckMsg(checkMsg)
			return
		}

		var formFile *multipart.FileHeader
		formFile, err = c.FormFile("file")
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		req.File = formFile
		if services.FacilityAiFileMap[req.FileCode] {
			aiResp, err := services.AiService.AiDateAndNumber(db, formFile)
			if err != nil {
				nc.ErrorResponse(req, err)
				return
			}
			req.AiDateAndNumber = aiResp
		}

		tx := db.Begin()
		resp, err := services.FacilityFileService.Upload(tx, req)
		if err != nil {
			tx.Rollback()
			nc.ErrorResponse(req, err)
			return
		}
		tx.Commit()
		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}
