package services

import (
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"github.com/Norray/medic-crew/model"
	"github.com/Norray/xrocket/xamqp"
	"github.com/Norray/xrocket/xconfig"
	"github.com/Norray/xrocket/xhermes"
	"github.com/Norray/xrocket/xi18n"
	"github.com/Norray/xrocket/xmail"
	"github.com/Norray/xrocket/xmodel"
	"github.com/matcornic/hermes/v2"
	"github.com/nicksnyder/go-i18n/v2/i18n"
	uuid "github.com/satori/go.uuid"
	log "github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// 統一電郵任務常量
const EmailNotificationTask = "email_notification_task"

// 電郵類型常量
const (
	// 管理員通知
	EmailTypeAdminNewFacilityApplication = "ADMIN_NEW_FACILITY_APPLICATION" // 新機構申請通知
	EmailTypeAdminHelpDeskNotification   = "ADMIN_HELP_DESK_NOTIFICATION"   // 協助工單通知

	// 機構通知
	EmailTypeFacilityAgreementReady           = "FACILITY_AGREEMENT_READY"            // 協議準備完成通知
	EmailTypeFacilityAgreementRenewalReminder = "FACILITY_AGREEMENT_RENEWAL_REMINDER" // 協議續期提醒通知
	EmailTypeFacilityAgreementExpired         = "FACILITY_AGREEMENT_EXPIRED"          // 協議過期通知
	EmailTypeFacilityProfileApproved          = "FACILITY_APPROVED"                   // 機構資料審核通過通知
	EmailTypeFacilityProfileRejected          = "FACILITY_REJECTED"                   // 機構資料審核拒絕通知
	EmailTypeFacilityInvoiceNotification      = "FACILITY_INVOICE_NOTIFICATION"       // 發票通知
	EmailTypeFacilityUrgentShiftUnfilled      = "FACILITY_URGENT_SHIFT_UNFILLED"      // 緊急工作未填滿通知
	EmailTypeFacilityJobCancellation          = "FACILITY_JOB_CANCELLATION"           // 機構取消工作通知

	// 專業人士通知
	EmailTypeProfessionalRejected        = "PROFESSIONAL_REJECTED"         // 專業人士資料審核拒絕通知
	EmailTypeProfessionalApproved        = "PROFESSIONAL_APPROVED"         // 專業人士資料審核通過通知
	EmailTypeProfessionalJobCancellation = "PROFESSIONAL_JOB_CANCELLATION" // 專業人士取消工作通知
)

// 統一電郵任務請求結構
type EmailNotificationTaskReq struct {
	EmailType string      `json:"emailType"` // 電郵類型
	Data      interface{} `json:"data"`      // 電郵數據
}

// SendEmailTask 發送電郵任務到隊列
func (s *emailNotificationService) SendEmailTask(emailType string, data interface{}) error {
	taskReq := EmailNotificationTaskReq{
		EmailType: emailType,
		Data:      data,
	}

	reqData, err := json.Marshal(taskReq)
	if err != nil {
		return err
	}

	return xamqp.SendTask(EmailNotificationTask, xamqp.Task{
		MessageId: EmailNotificationTask,
		TaskId:    EmailNotificationTask + "_" + emailType + "_" + uuid.NewV4().String(),
		Data:      string(reqData),
	})
}

var EmailNotificationService = new(emailNotificationService)

type emailNotificationService struct{}

// 專業人士提醒信息結構
type ProfessionalReminderInfo struct {
	ProfessionalUserId uint64 // 專業人士用戶ID
	ProfessionalId     uint64 // 專業人士ID
	Email              string // 專業人士郵箱
	FirstName          string // 名字
	LastName           string // 姓氏
	Lang               string // 語言
}

// 機構提醒信息結構
type FacilityReminderInfo struct {
	FacilityId   uint64 // 機構ID
	Email        string // 機構郵箱
	FacilityName string // 機構名稱
	Lang         string // 語言
}

// 工作相關提醒信息結構（擴展 JobReminderInfo）
type JobEmailReminderInfo struct {
	NotificationUserId uint64    // 接收通知的用戶ID
	FacilityId         uint64    // 機構ID
	JobId              uint64    // 工作ID
	JobApplicationId   uint64    // 工作申請ID
	DocumentId         uint64    // 單據ID
	BeginTime          time.Time // 工作開始時間
	Email              string    // 用戶郵箱
	UserName           string    // 用戶名稱
	FacilityName       string    // 機構名稱
	Lang               string    // 語言
}

// 管理員提醒信息結構
type AdminReminderInfo struct {
	AdminEmail    []string // 管理員郵箱
	Lang          string   // 語言
	RequesterName string   // 請求者名稱（用於協助工單）
	FacilityId    uint64
	FacilityName  string // 機構名稱（用於新機構申請）
}

// 協議提醒信息結構
type AgreementReminderInfo struct {
	FacilityId   uint64 // 機構ID
	AgreementId  uint64 // 協議ID
	Email        string // 機構郵箱
	FacilityName string // 機構名稱
	Lang         string // 語言
}

// region ---------------------------------------------------- 1. 新機構申請通知 (admin) ----------------------------------------------------

// 新機構申請通知請求
type NewFacilityApplicationReq struct {
	FacilityId        uint64   `json:"facilityId"`        // 機構ID
	FacilityProfileId uint64   `json:"facilityProfileId"` // 機構資料ID
	Lang              string   `json:"lang"`              // 語言
	AdminEmail        []string `json:"adminEmail"`        // 管理員郵箱
}

// 處理新機構申請通知任務 - 通知系統管理員
func ProcessAdminNewFacilityApplication(db *gorm.DB, data interface{}) error {
	dataBytes, err := json.Marshal(data)
	if err != nil {
		return err
	}

	var req NewFacilityApplicationReq
	err = json.Unmarshal(dataBytes, &req)
	if err != nil {
		return err
	}

	// 查詢所有系統管理員用戶
	// 獲取系統管理員列表
	adminUsers, err := SystemNotificationService.GetSystemAdminUsers(db)
	if err != nil {
		return err
	}
	if len(adminUsers) == 0 {
		return nil // 沒有管理員用戶，直接返回
	}
	adminEmails := make([]string, len(adminUsers))
	for i, admin := range adminUsers {
		adminEmails[i] = admin.Email
	}

	// 為每個管理員發送通知
	adminReq := NewFacilityApplicationReq{
		FacilityId:        req.FacilityId,
		FacilityProfileId: req.FacilityProfileId,
		Lang:              req.Lang,
		AdminEmail:        adminEmails,
	}

	err = EmailNotificationService.SendAdminNewFacilityApplicationNotification(db, adminReq)
	if err != nil {
		return err
	}

	return nil
}

// 發送新機構申請通知 - 通知系統管理員
func (s *emailNotificationService) SendAdminNewFacilityApplicationNotification(db *gorm.DB, req NewFacilityApplicationReq) error {
	// 獲取管理員審核機構 URL
	taskUrl, err := CommonSettingService.GetSettingValueByCode(db, model.CommonSettingCodeAdminApproveFacilityUrl)
	if err != nil {
		return err
	}
	facilityProfileIdStr := strconv.FormatUint(req.FacilityProfileId, 10)
	facilityIdStr := strconv.FormatUint(req.FacilityId, 10)
	timeStr := strconv.FormatInt(time.Now().Unix(), 10)
	taskUrl = fmt.Sprintf("%s/%s/%s/approval?_t=%s", taskUrl, facilityProfileIdStr, facilityIdStr, timeStr)

	emailGreeting := i18n.Message{
		ID:    "email.new_facility_application.greeting",
		Other: "Hi",
	}
	emailSignature := i18n.Message{
		ID:    "email.new_facility_application.signature",
		Other: "Thank you,\nMedic Crew",
	}
	emailSubject := i18n.Message{
		ID:    "email.new_facility_application.subject",
		Other: "New Facility Application Received",
	}
	emailContent := i18n.Message{
		ID:    "email.new_facility_application.content",
		Other: "This is an automated notification to inform you that a new facility has submitted an application to join the Medic Crew platform.\n\nPlease log in to your admin account to review the application and verify the submitted details.",
	}
	emailButtonText := i18n.Message{
		ID:    "email.new_facility_application.button",
		Other: "Click here to Go To Task",
	}

	// 構建電郵內容
	body := hermes.Body{
		Greeting:  xi18n.LocalizeWithLang(req.Lang, &emailGreeting),
		Name:      "",
		Signature: xi18n.LocalizeWithLang(req.Lang, &emailSignature),
		Intros: []string{
			xi18n.LocalizeWithLang(req.Lang, &emailContent),
		},
		Actions: []hermes.Action{
			{
				Instructions: "",
				Button: hermes.Button{
					Color: MedicCrewButtonColor,
					Text:  xi18n.LocalizeWithLang(req.Lang, &emailButtonText),
					Link:  taskUrl,
				},
			},
		},
	}

	content, err := xhermes.GenerateHTML(HermesDefaultProduct(medicCrewLogoUrl), body)
	if err != nil {
		return err
	}

	err = xmail.SendMailToMultiple(req.AdminEmail, []string{}, fmt.Sprintf("[%s] %s", xconfig.MailConf.Name, xi18n.LocalizeWithLang(req.Lang, &emailSubject)), "text/html", content)
	if err != nil {
		return err
	}

	return nil
}

// endregion ---------------------------------------------------- 1. 新機構申請通知 (admin) ----------------------------------------------------

// region ---------------------------------------------------- 2. 協議準備完成通知 (facility) ----------------------------------------------------

// 協議準備完成通知請求
type FacilityAgreementReadyReq struct {
	Lang         string `json:"lang"`         // 語言
	FacilityName string `json:"facilityName"` // 機構名稱
	Email        string `json:"email"`        // 機構郵箱
}

// 處理協議準備完成通知任務 - 通知機構
func ProcessFacilityAgreementReady(db *gorm.DB, data interface{}) error {
	dataBytes, err := json.Marshal(data)
	if err != nil {
		return err
	}

	var req FacilityAgreementReadyReq
	err = json.Unmarshal(dataBytes, &req)
	if err != nil {
		return err
	}

	return EmailNotificationService.SendFacilityAgreementReadyNotification(db, req)
}

// 發送協議準備完成通知 - 通知機構
func (s *emailNotificationService) SendFacilityAgreementReadyNotification(db *gorm.DB, req FacilityAgreementReadyReq) error {
	// 獲取機構基本資料 URL
	agreementUrl, err := CommonSettingService.GetSettingValueByCode(db, model.CommonSettingCodeFacilityBasicUrl)
	if err != nil {
		return err
	}

	i18nMap := map[string]string{
		"FacilityName": req.FacilityName,
	}

	emailGreeting := i18n.Message{
		ID:    "email.agreement_ready.greeting",
		Other: "Hi {{.FacilityName}}",
	}
	emailSignature := i18n.Message{
		ID:    "email.agreement_ready.signature",
		Other: "Thank you,\nMedic Crew",
	}
	emailSubject := i18n.Message{
		ID:    "email.agreement_ready.subject",
		Other: "Agreement Ready for Review and Completion on Medic Crew",
	}
	emailContent := i18n.Message{
		ID:    "email.agreement_ready.content",
		Other: "We have reviewed the information provided by your facility and prepared a draft agreement accordingly.\n\nTo proceed, please log in to Medic Crew to complete your facility details and sign the agreement. You can use the same login credentials (email and password) created during your initial registration.\n\nIf you have any questions or need assistance, please don't hesitate to contact us.",
	}
	emailButtonText := i18n.Message{
		ID:    "email.agreement_ready.button",
		Other: "Click here to Review Agreement",
	}

	// 構建電郵內容
	body := hermes.Body{
		Greeting:  xi18n.LocalizeWithLangAndTemplateData(req.Lang, &emailGreeting, i18nMap),
		Name:      "",
		Signature: xi18n.LocalizeWithLangAndTemplateData(req.Lang, &emailSignature, i18nMap),
		Intros: []string{
			xi18n.LocalizeWithLangAndTemplateData(req.Lang, &emailContent, i18nMap),
		},
		Actions: []hermes.Action{
			{
				Instructions: "",
				Button: hermes.Button{
					Color: MedicCrewButtonColor,
					Text:  xi18n.LocalizeWithLangAndTemplateData(req.Lang, &emailButtonText, i18nMap),
					Link:  agreementUrl,
				},
			},
		},
	}

	content, err := xhermes.GenerateHTML(HermesDefaultProduct(medicCrewLogoUrl), body)
	if err != nil {
		return err
	}

	err = xmail.SendMail(req.Email, fmt.Sprintf("[%s] %s", xconfig.MailConf.Name, xi18n.LocalizeWithLangAndTemplateData(req.Lang, &emailSubject, i18nMap)), "text/html", content)
	if err != nil {
		return err
	}

	return nil
}

// endregion ---------------------------------------------------- 2. 協議準備完成通知 (facility) ----------------------------------------------------

// region ---------------------------------------------------- 3. 協議續期提醒 (admin) ----------------------------------------------------

// 協議續期提醒請求
type FacilityAgreementRenewalReminderReq struct {
	Lang         string   `json:"lang"`         // 語言
	FacilityName string   `json:"facilityName"` // 機構名稱
	AdminEmail   []string `json:"adminEmail"`   // 管理員郵箱
}

// 處理協議續期提醒任務 - 通知管理員
func ProcessFacilityAgreementRenewalReminder(db *gorm.DB, data interface{}) error {
	dataBytes, err := json.Marshal(data)
	if err != nil {
		return err
	}

	var req FacilityAgreementRenewalReminderReq
	err = json.Unmarshal(dataBytes, &req)
	if err != nil {
		return err
	}

	return EmailNotificationService.SendFacilityAgreementRenewalReminderNotification(db, req)
}

// 發送協議續期提醒 - 通知機構
func (s *emailNotificationService) SendFacilityAgreementRenewalReminderNotification(db *gorm.DB, req FacilityAgreementRenewalReminderReq) error {
	// 獲取機構協議 URL
	renewalUrl, err := CommonSettingService.GetSettingValueByCode(db, model.CommonSettingCodeFacilityAgreementUrl)
	if err != nil {
		return err
	}

	i18nMap := map[string]string{
		"FacilityName": req.FacilityName,
	}

	emailGreeting := i18n.Message{
		ID:    "email.agreement_renewal_reminder.greeting",
		Other: "Hi {{.FacilityName}}",
	}
	emailSignature := i18n.Message{
		ID:    "email.agreement_renewal_reminder.signature",
		Other: "Thank you,\nMedic Crew",
	}
	emailSubject := i18n.Message{
		ID:    "email.agreement_renewal_reminder.subject",
		Other: "Medic Crew Agreement Renewal Reminder",
	}
	emailContent := i18n.Message{
		ID:    "email.agreement_renewal_reminder.content",
		Other: "This is a friendly reminder that the agreement between your facility and Medic Crew is set to expire in one month. To ensure continued service without interruption, please log in to your account and finalise a new agreement at your earliest convenience.",
	}
	emailButtonText := i18n.Message{
		ID:    "email.agreement_renewal_reminder.button",
		Other: "Click here to Renew Agreement",
	}

	// 構建電郵內容
	body := hermes.Body{
		Greeting:  xi18n.LocalizeWithLangAndTemplateData(req.Lang, &emailGreeting, i18nMap),
		Name:      "",
		Signature: xi18n.LocalizeWithLangAndTemplateData(req.Lang, &emailSignature, i18nMap),
		Intros: []string{
			xi18n.LocalizeWithLangAndTemplateData(req.Lang, &emailContent, i18nMap),
		},
		Actions: []hermes.Action{
			{
				Instructions: "",
				Button: hermes.Button{
					Color: MedicCrewButtonColor,
					Text:  xi18n.LocalizeWithLangAndTemplateData(req.Lang, &emailButtonText, i18nMap),
					Link:  renewalUrl,
				},
			},
		},
	}

	content, err := xhermes.GenerateHTML(HermesDefaultProduct(medicCrewLogoUrl), body)
	if err != nil {
		return err
	}

	err = xmail.SendMailToMultiple(req.AdminEmail, []string{}, fmt.Sprintf("[%s] %s", xconfig.MailConf.Name, xi18n.LocalizeWithLangAndTemplateData(req.Lang, &emailSubject, i18nMap)), "text/html", content)
	if err != nil {
		return err
	}

	return nil
}

// endregion ---------------------------------------------------- 3. 協議續期提醒 (admin) ----------------------------------------------------

// region ---------------------------------------------------- 4. 協議過期通知 (facility) ----------------------------------------------------

// 協議過期通知請求
type FacilityAgreementExpiredReq struct {
	Lang         string `json:"lang"`         // 語言
	FacilityName string `json:"facilityName"` // 機構名稱
	Email        string `json:"email"`        // 機構郵箱
}

// 處理協議過期通知任務 - 通知機構
func ProcessFacilityAgreementExpired(db *gorm.DB, data interface{}) error {
	dataBytes, err := json.Marshal(data)
	if err != nil {
		return err
	}

	var req FacilityAgreementExpiredReq
	err = json.Unmarshal(dataBytes, &req)
	if err != nil {
		return err
	}

	return EmailNotificationService.SendFacilityAgreementExpiredNotification(db, req)
}

// 發送協議過期通知
func (s *emailNotificationService) SendFacilityAgreementExpiredNotification(db *gorm.DB, req FacilityAgreementExpiredReq) error {
	// 獲取機構基本資料 URL
	renewalUrl, err := CommonSettingService.GetSettingValueByCode(db, model.CommonSettingCodeFacilityBasicUrl)
	if err != nil {
		return err
	}

	i18nMap := map[string]string{
		"FacilityName": req.FacilityName,
	}

	emailGreeting := i18n.Message{
		ID:    "email.agreement_expired.greeting",
		Other: "Hi {{.FacilityName}}",
	}
	emailSignature := i18n.Message{
		ID:    "email.agreement_expired.signature",
		Other: "Thank you,\nMedic Crew",
	}
	emailSubject := i18n.Message{
		ID:    "email.agreement_expired.subject",
		Other: "Action Required: Your Agreement has Expired",
	}
	emailContent := i18n.Message{
		ID:    "email.agreement_expired.content",
		Other: "This is to inform you that your agreement with Medic Crew has expired. To continue posting jobs and using our platform, please log in to your account to sign a new agreement.",
	}
	emailButtonText := i18n.Message{
		ID:    "email.agreement_expired.button",
		Other: "Click here to Renew Agreement",
	}

	// 構建電郵內容
	body := hermes.Body{
		Greeting:  xi18n.LocalizeWithLangAndTemplateData(req.Lang, &emailGreeting, i18nMap),
		Name:      "",
		Signature: xi18n.LocalizeWithLangAndTemplateData(req.Lang, &emailSignature, i18nMap),
		Intros: []string{
			xi18n.LocalizeWithLangAndTemplateData(req.Lang, &emailContent, i18nMap),
		},
		Actions: []hermes.Action{
			{
				Instructions: "",
				Button: hermes.Button{
					Color: MedicCrewButtonColor,
					Text:  xi18n.LocalizeWithLangAndTemplateData(req.Lang, &emailButtonText, i18nMap),
					Link:  renewalUrl,
				},
			},
		},
	}

	content, err := xhermes.GenerateHTML(HermesDefaultProduct(medicCrewLogoUrl), body)
	if err != nil {
		return err
	}

	err = xmail.SendMail(req.Email, fmt.Sprintf("[%s] %s", xconfig.MailConf.Name, xi18n.LocalizeWithLangAndTemplateData(req.Lang, &emailSubject, i18nMap)), "text/html", content)
	if err != nil {
		return err
	}

	return nil
}

// endregion ---------------------------------------------------- 4. 協議過期通知 (facility) ----------------------------------------------------

// region ---------------------------------------------------- 5. 機構資料審核通過 (facility) ----------------------------------------------------

// 機構資料審核通過請求
type FacilityApprovedReq struct {
	Lang         string `json:"lang"`         // 語言
	FacilityName string `json:"facilityName"` // 機構名稱
	Email        string `json:"email"`        // 機構郵箱
}

// 處理機構資料審核通過通知任務
func ProcessFacilityApproved(db *gorm.DB, data interface{}) error {
	dataBytes, err := json.Marshal(data)
	if err != nil {
		return err
	}

	var req ApprovalEmailTaskReq
	err = json.Unmarshal(dataBytes, &req)
	if err != nil {
		return err
	}

	// 查詢機構信息和用戶信息
	var facilityProfile model.FacilityProfile

	err = db.Where("facility_id = ? AND id = ?", req.FacilityId, req.FacilityProfileId).First(&facilityProfile).Error
	if err != nil {
		return fmt.Errorf("failed to query facility profile: %v", err)
	}

	// 構建電郵請求
	emailReq := FacilityApprovedReq{
		Lang:         req.Lang,
		FacilityName: facilityProfile.Name,
		Email:        facilityProfile.Email,
	}

	return EmailNotificationService.SendFacilityApprovedNotification(db, emailReq)
}

// 發送機構資料審核通過通知
func (s *emailNotificationService) SendFacilityApprovedNotification(db *gorm.DB, req FacilityApprovedReq) error {
	// 獲取用戶登入 URL
	loginUrl, err := CommonSettingService.GetSettingValueByCode(db, model.CommonSettingCodeUserLoginUrl)
	if err != nil {
		return err
	}

	i18nMap := map[string]string{
		"FacilityName": req.FacilityName,
	}

	emailGreeting := i18n.Message{
		ID:    "email.facility_approved.greeting",
		Other: "Hi {{.FacilityName}}",
	}
	emailSignature := i18n.Message{
		ID:    "email.facility_approved.signature",
		Other: "Thank you,\nMedic Crew",
	}
	emailSubject := i18n.Message{
		ID:    "email.facility_approved.subject",
		Other: "Your Facility's Information Has Been Approved",
	}
	emailContent := i18n.Message{
		ID:    "email.facility_approved.content",
		Other: "We are pleased to inform you that we have reviewed and approved the detailed information you provided for your facility. You can now log in to your account and start posting jobs to find the right professionals for your facility.",
	}
	emailButtonText := i18n.Message{
		ID:    "email.facility_approved.button",
		Other: "Click here to Login",
	}

	// 構建電郵內容
	body := hermes.Body{
		Greeting:  xi18n.LocalizeWithLangAndTemplateData(req.Lang, &emailGreeting, i18nMap),
		Name:      "",
		Signature: xi18n.LocalizeWithLangAndTemplateData(req.Lang, &emailSignature, i18nMap),
		Intros: []string{
			xi18n.LocalizeWithLangAndTemplateData(req.Lang, &emailContent, i18nMap),
		},
		Actions: []hermes.Action{
			{
				Instructions: "",
				Button: hermes.Button{
					Color: MedicCrewButtonColor,
					Text:  xi18n.LocalizeWithLangAndTemplateData(req.Lang, &emailButtonText, i18nMap),
					Link:  loginUrl,
				},
			},
		},
	}

	content, err := xhermes.GenerateHTML(HermesDefaultProduct(medicCrewLogoUrl), body)
	if err != nil {
		return err
	}

	err = xmail.SendMail(req.Email, fmt.Sprintf("[%s] %s", xconfig.MailConf.Name, xi18n.LocalizeWithLangAndTemplateData(req.Lang, &emailSubject, i18nMap)), "text/html", content)
	if err != nil {
		return err
	}

	return nil
}

// endregion ---------------------------------------------------- 5. 機構資料審核通過 (facility) ----------------------------------------------------

// region ---------------------------------------------------- 6. 機構資料審核拒絕 (facility) ----------------------------------------------------

// 機構資料審核拒絕請求
type FacilityRejectedReq struct {
	Lang         string `json:"lang"`         // 語言
	FacilityName string `json:"facilityName"` // 機構名稱
	Email        string `json:"email"`        // 機構郵箱
}

// 處理機構資料審核拒絕通知任務
func ProcessFacilityRejected(db *gorm.DB, data interface{}) error {
	dataBytes, err := json.Marshal(data)
	if err != nil {
		return err
	}

	var req ApprovalEmailTaskReq
	err = json.Unmarshal(dataBytes, &req)
	if err != nil {
		return err
	}

	// 查詢機構信息和用戶信息
	var facilityProfile model.FacilityProfile

	err = db.Where("facility_id = ? AND id = ?", req.FacilityId, req.FacilityProfileId).First(&facilityProfile).Error
	if err != nil {
		return fmt.Errorf("failed to query facility profile: %v", err)
	}

	// 構建電郵請求
	emailReq := FacilityRejectedReq{
		Lang:         req.Lang,
		FacilityName: facilityProfile.Name,
		Email:        facilityProfile.Email,
	}

	return EmailNotificationService.SendFacilityRejectedNotification(db, emailReq)
}

// 發送機構資料審核拒絕通知
func (s *emailNotificationService) SendFacilityRejectedNotification(db *gorm.DB, req FacilityRejectedReq) error {
	// 獲取機構基本資料 URL
	reviewUrl, err := CommonSettingService.GetSettingValueByCode(db, model.CommonSettingCodeFacilityBasicUrl)
	if err != nil {
		return err
	}

	i18nMap := map[string]string{
		"FacilityName": req.FacilityName,
	}

	emailGreeting := i18n.Message{
		ID:    "email.facility_rejected.greeting",
		Other: "Hi {{.FacilityName}}",
	}
	emailSignature := i18n.Message{
		ID:    "email.facility_rejected.signature",
		Other: "Thank you,\nMedic Crew",
	}
	emailSubject := i18n.Message{
		ID:    "email.facility_rejected.subject",
		Other: "Action Required: Your Application Needs Attention",
	}
	emailContent := i18n.Message{
		ID:    "email.facility_rejected.content",
		Other: "We have reviewed the information you submitted for your facility, but we were unable to approve your account at this time. This may be due to incomplete or incorrect details. Please log in to your account to review and resubmit your information.",
	}
	emailButtonText := i18n.Message{
		ID:    "email.facility_rejected.button",
		Other: "Click here to Review Your Application",
	}

	// 構建電郵內容
	body := hermes.Body{
		Greeting:  xi18n.LocalizeWithLangAndTemplateData(req.Lang, &emailGreeting, i18nMap),
		Name:      "",
		Signature: xi18n.LocalizeWithLangAndTemplateData(req.Lang, &emailSignature, i18nMap),
		Intros: []string{
			xi18n.LocalizeWithLangAndTemplateData(req.Lang, &emailContent, i18nMap),
		},
		Actions: []hermes.Action{
			{
				Instructions: "",
				Button: hermes.Button{
					Color: MedicCrewButtonColor,
					Text:  xi18n.LocalizeWithLangAndTemplateData(req.Lang, &emailButtonText, i18nMap),
					Link:  reviewUrl,
				},
			},
		},
	}

	content, err := xhermes.GenerateHTML(HermesDefaultProduct(medicCrewLogoUrl), body)
	if err != nil {
		return err
	}

	err = xmail.SendMail(req.Email, fmt.Sprintf("[%s] %s", xconfig.MailConf.Name, xi18n.LocalizeWithLangAndTemplateData(req.Lang, &emailSubject, i18nMap)), "text/html", content)
	if err != nil {
		return err
	}

	return nil
}

// endregion ---------------------------------------------------- 6. 機構資料審核拒絕 (facility) ----------------------------------------------------

// region ---------------------------------------------------- 7. 專業人士資料審核拒絕 (professional) ----------------------------------------------------

// 專業人士資料審核拒絕請求
type ProfessionalRejectedReq struct {
	Lang             string `json:"lang"`             // 語言
	ProfessionalName string `json:"professionalName"` // 專業人士名稱
	Email            string `json:"email"`            // 專業人士郵箱
}

// 發送專業人士資料審核拒絕通知
func (s *emailNotificationService) SendProfessionalRejectedNotification(db *gorm.DB, req ProfessionalRejectedReq) error {
	// 獲取機構基本資料 URL
	reviewUrl, err := CommonSettingService.GetSettingValueByCode(db, model.CommonSettingCodeFacilityBasicUrl)
	if err != nil {
		return err
	}

	i18nMap := map[string]string{
		"ProfessionalName": req.ProfessionalName,
	}

	emailGreeting := i18n.Message{
		ID:    "email.professional_rejected.greeting",
		Other: "Hi {{.ProfessionalName}}",
	}
	emailSignature := i18n.Message{
		ID:    "email.professional_rejected.signature",
		Other: "Thank you,\nMedic Crew",
	}
	emailSubject := i18n.Message{
		ID:    "email.professional_rejected.subject",
		Other: "Action Required: Your Profile Needs Attention",
	}
	emailContent := i18n.Message{
		ID:    "email.professional_rejected.content",
		Other: "We have reviewed the detailed information you provided, but we were unable to approve your professional profile at this time. This may be due to incomplete or incorrect details. Please log in to your account to review and resubmit your information.",
	}
	emailButtonText := i18n.Message{
		ID:    "email.professional_rejected.button",
		Other: "Click here to Review Your Application",
	}

	// 構建電郵內容
	body := hermes.Body{
		Greeting:  xi18n.LocalizeWithLangAndTemplateData(req.Lang, &emailGreeting, i18nMap),
		Name:      "",
		Signature: xi18n.LocalizeWithLangAndTemplateData(req.Lang, &emailSignature, i18nMap),
		Intros: []string{
			xi18n.LocalizeWithLangAndTemplateData(req.Lang, &emailContent, i18nMap),
		},
		Actions: []hermes.Action{
			{
				Instructions: "",
				Button: hermes.Button{
					Color: MedicCrewButtonColor,
					Text:  xi18n.LocalizeWithLangAndTemplateData(req.Lang, &emailButtonText, i18nMap),
					Link:  reviewUrl,
				},
			},
		},
	}

	content, err := xhermes.GenerateHTML(HermesDefaultProduct(medicCrewLogoUrl), body)
	if err != nil {
		return err
	}

	err = xmail.SendMail(req.Email, fmt.Sprintf("[%s] %s", xconfig.MailConf.Name, xi18n.LocalizeWithLangAndTemplateData(req.Lang, &emailSubject, i18nMap)), "text/html", content)
	if err != nil {
		return err
	}

	return nil
}

// endregion ---------------------------------------------------- 7. 專業人士資料審核拒絕 (professional) ----------------------------------------------------

// region ---------------------------------------------------- 8. 專業人士資料審核通過 (professional) ----------------------------------------------------

// 專業人士資料審核通過請求
type ProfessionalApprovedReq struct {
	Lang             string `json:"lang"`             // 語言
	ProfessionalName string `json:"professionalName"` // 專業人士名稱
	Email            string `json:"email"`            // 專業人士郵箱
}

// 發送專業人士資料審核通過通知
func (s *emailNotificationService) SendProfessionalApprovedNotification(db *gorm.DB, req ProfessionalApprovedReq) error {
	// 獲取用戶登入 URL
	loginUrl, err := CommonSettingService.GetSettingValueByCode(db, model.CommonSettingCodeUserLoginUrl)
	if err != nil {
		return err
	}

	i18nMap := map[string]string{
		"ProfessionalName": req.ProfessionalName,
	}

	emailGreeting := i18n.Message{
		ID:    "email.professional_approved.greeting",
		Other: "Hi {{.ProfessionalName}}",
	}
	emailSignature := i18n.Message{
		ID:    "email.professional_approved.signature",
		Other: "Thank you,\nMedic Crew",
	}
	emailSubject := i18n.Message{
		ID:    "email.professional_approved.subject",
		Other: "Your Profile Has Been Approved!",
	}
	emailContent := i18n.Message{
		ID:    "email.professional_approved.content",
		Other: "We have reviewed and approved your professional profile on the Medic Crew platform. You are now ready to start applying for jobs that match your skills and experience! We wish you success in your job search.",
	}
	emailButtonText := i18n.Message{
		ID:    "email.professional_approved.button",
		Other: "Click here to Login",
	}

	// 構建電郵內容
	body := hermes.Body{
		Greeting:  xi18n.LocalizeWithLangAndTemplateData(req.Lang, &emailGreeting, i18nMap),
		Name:      "",
		Signature: xi18n.LocalizeWithLangAndTemplateData(req.Lang, &emailSignature, i18nMap),
		Intros: []string{
			xi18n.LocalizeWithLangAndTemplateData(req.Lang, &emailContent, i18nMap),
		},
		Actions: []hermes.Action{
			{
				Instructions: "",
				Button: hermes.Button{
					Color: MedicCrewButtonColor,
					Text:  xi18n.LocalizeWithLangAndTemplateData(req.Lang, &emailButtonText, i18nMap),
					Link:  loginUrl,
				},
			},
		},
	}

	content, err := xhermes.GenerateHTML(HermesDefaultProduct(medicCrewLogoUrl), body)
	if err != nil {
		return err
	}

	err = xmail.SendMail(req.Email, fmt.Sprintf("[%s] %s", xconfig.MailConf.Name, xi18n.LocalizeWithLangAndTemplateData(req.Lang, &emailSubject, i18nMap)), "text/html", content)
	if err != nil {
		return err
	}

	return nil
}

// endregion ---------------------------------------------------- 8. 專業人士資料審核通過 (professional) ----------------------------------------------------

// region ---------------------------------------------------- 9. 發票通知 (facility) ----------------------------------------------------

// 發票通知請求
type FacilityInvoiceNotificationReq struct {
	Lang         string `json:"lang"`         // 語言
	FacilityName string `json:"facilityName"` // 機構名稱
	Email        string `json:"email"`        // 機構郵箱
	DocumentId   uint64 `json:"documentId"`   // 單據ID
}

// 發送發票通知
func (s *emailNotificationService) SendFacilityInvoiceNotification(db *gorm.DB, req FacilityInvoiceNotificationReq) error {
	// 獲取發票預覽 URL
	invoiceUrl, err := CommonSettingService.GetSettingValueByCode(db, model.CommonSettingCodeFacilityInvoicePreviewUrl)
	if err != nil {
		return err
	}
	invoiceIdStr := strconv.FormatUint(req.DocumentId, 10)
	invoiceUrl = fmt.Sprintf("%s/%s", invoiceUrl, FrontendEncryptService.Encrypt(invoiceIdStr))

	i18nMap := map[string]string{
		"FacilityName": req.FacilityName,
	}

	emailGreeting := i18n.Message{
		ID:    "email.invoice_notification.greeting",
		Other: "Hi {{.FacilityName}}",
	}
	emailSignature := i18n.Message{
		ID:    "email.invoice_notification.signature",
		Other: "Thank you,\nMedic Crew",
	}
	emailSubject := i18n.Message{
		ID:    "email.invoice_notification.subject",
		Other: "Medic Crew Invoice",
	}
	emailContent := i18n.Message{
		ID:    "email.invoice_notification.content",
		Other: "Your invoice has been generated. Please arrange payment at your earliest convenience to ensure the smooth continuation of services.",
	}
	emailButtonText := i18n.Message{
		ID:    "email.invoice_notification.button",
		Other: "Click here to view the invoice",
	}

	// 構建電郵內容
	body := hermes.Body{
		Greeting:  xi18n.LocalizeWithLangAndTemplateData(req.Lang, &emailGreeting, i18nMap),
		Name:      "",
		Signature: xi18n.LocalizeWithLangAndTemplateData(req.Lang, &emailSignature, i18nMap),
		Intros: []string{
			xi18n.LocalizeWithLangAndTemplateData(req.Lang, &emailContent, i18nMap),
		},
		Actions: []hermes.Action{
			{
				Instructions: "",
				Button: hermes.Button{
					Color: MedicCrewButtonColor,
					Text:  xi18n.LocalizeWithLangAndTemplateData(req.Lang, &emailButtonText, i18nMap),
					Link:  invoiceUrl,
				},
			},
		},
	}

	content, err := xhermes.GenerateHTML(HermesDefaultProduct(medicCrewLogoUrl), body)
	if err != nil {
		return err
	}

	err = xmail.SendMail(req.Email, fmt.Sprintf("[%s] %s", xconfig.MailConf.Name, xi18n.LocalizeWithLangAndTemplateData(req.Lang, &emailSubject, i18nMap)), "text/html", content)
	if err != nil {
		return err
	}

	return nil
}

// endregion ---------------------------------------------------- 9. 發票通知 (facility) ----------------------------------------------------

// region ---------------------------------------------------- 10. 協助工單通知 (admin) ----------------------------------------------------

// 協助工單通知請求
type AdminHelpDeskNotificationReq struct {
	Lang          string   `json:"lang"`          // 語言
	RequesterName string   `json:"requesterName"` // 請求者名稱 (Professional Name 或 Facility Name)
	AdminEmail    []string `json:"adminEmail"`    // 管理員郵箱
}

// 處理協助工單通知任務 - 通知系統管理員
func ProcessAdminHelpDeskNotification(db *gorm.DB, data interface{}) error {
	dataBytes, err := json.Marshal(data)
	if err != nil {
		return err
	}

	var req AdminHelpDeskNotificationReq
	err = json.Unmarshal(dataBytes, &req)
	if err != nil {
		return err
	}

	return EmailNotificationService.SendAdminHelpDeskNotification(db, req)
}

// 發送協助工單通知 - 通知系統管理員
func (s *emailNotificationService) SendAdminHelpDeskNotification(db *gorm.DB, req AdminHelpDeskNotificationReq) error {
	// 獲取管理員儀表板 URL
	dashboardUrl, err := CommonSettingService.GetSettingValueByCode(db, model.CommonSettingCodeAdminDashboardUrl)
	if err != nil {
		return err
	}

	i18nMap := map[string]string{
		"RequesterName": req.RequesterName,
	}

	emailGreeting := i18n.Message{
		ID:    "email.help_desk_notification.greeting",
		Other: "Hi",
	}
	emailSignature := i18n.Message{
		ID:    "email.help_desk_notification.signature",
		Other: "Thank you,\nMedic Crew",
	}
	emailSubject := i18n.Message{
		ID:    "email.help_desk_notification.subject",
		Other: "Professional Shift Started",
	}
	emailContent := i18n.Message{
		ID:    "email.help_desk_notification.content",
		Other: "A new help desk request has been submitted by {{.RequesterName}}.\nPlease log in to your admin account to review the request and provide a timely response.",
	}
	emailButtonText := i18n.Message{
		ID:    "email.help_desk_notification.button",
		Other: "Click here to Go To Task",
	}

	// 構建電郵內容
	body := hermes.Body{
		Greeting:  xi18n.LocalizeWithLangAndTemplateData(req.Lang, &emailGreeting, i18nMap),
		Name:      "",
		Signature: xi18n.LocalizeWithLangAndTemplateData(req.Lang, &emailSignature, i18nMap),
		Intros: []string{
			xi18n.LocalizeWithLangAndTemplateData(req.Lang, &emailContent, i18nMap),
		},
		Actions: []hermes.Action{
			{
				Instructions: "",
				Button: hermes.Button{
					Color: MedicCrewButtonColor,
					Text:  xi18n.LocalizeWithLangAndTemplateData(req.Lang, &emailButtonText, i18nMap),
					Link:  dashboardUrl,
				},
			},
		},
	}

	content, err := xhermes.GenerateHTML(HermesDefaultProduct(medicCrewLogoUrl), body)
	if err != nil {
		return err
	}

	err = xmail.SendMailToMultiple(req.AdminEmail, []string{}, fmt.Sprintf("[%s] %s", xconfig.MailConf.Name, xi18n.LocalizeWithLangAndTemplateData(req.Lang, &emailSubject, i18nMap)), "text/html", content)
	if err != nil {
		return err
	}

	return nil
}

// endregion ---------------------------------------------------- 10. 協助工單通知 (admin) ----------------------------------------------------

// region ---------------------------------------------------- 11. 工作未填滿緊急通知 (facility) ----------------------------------------------------

// 工作未填滿緊急通知請求
type FacilityUrgentShiftUnfilledReq struct {
	Lang         string `json:"lang"`         // 語言
	FacilityName string `json:"facilityName"` // 機構名稱
	Position     string `json:"position"`     // 職位
	StartTime    string `json:"startTime"`    // 開始時間
	Email        string `json:"email"`        // 機構郵箱
}

// 發送工作未填滿緊急通知 - 通知機構
func (s *emailNotificationService) SendFacilityUrgentShiftUnfilledNotification(db *gorm.DB, req FacilityUrgentShiftUnfilledReq) error {
	// 獲取機構已預約工作 URL
	shiftUrl, err := CommonSettingService.GetSettingValueByCode(db, model.CommonSettingCodeFacilityBookedJobsUrl)
	if err != nil {
		return err
	}

	i18nMap := map[string]string{
		"FacilityName": req.FacilityName,
		"Position":     req.Position,
		"StartTime":    req.StartTime,
	}

	emailGreeting := i18n.Message{
		ID:    "email.urgent_shift_unfilled.greeting",
		Other: "Hi {{.FacilityName}}",
	}
	emailSignature := i18n.Message{
		ID:    "email.urgent_shift_unfilled.signature",
		Other: "Thank you for your understanding,\nMedic Crew",
	}
	emailSubject := i18n.Message{
		ID:    "email.urgent_shift_unfilled.subject",
		Other: "Urgent: {{.Position}} Shift Starting at {{.StartTime}} Remains Unfilled",
	}
	emailContent := i18n.Message{
		ID:    "email.urgent_shift_unfilled.content",
		Other: "This is a courtesy notification regarding the {{.Position}} shift scheduled to start at {{.StartTime}}. Despite our efforts, we have not been able to secure a professional for this shift and it is likely to remain unfilled.\n\nWe recommend preparing alternative arrangements on your end.",
	}
	emailButtonText := i18n.Message{
		ID:    "email.urgent_shift_unfilled.button",
		Other: "Click here to view the shift",
	}

	// 構建電郵內容
	body := hermes.Body{
		Greeting:  xi18n.LocalizeWithLangAndTemplateData(req.Lang, &emailGreeting, i18nMap),
		Name:      "",
		Signature: xi18n.LocalizeWithLangAndTemplateData(req.Lang, &emailSignature, i18nMap),
		Intros: []string{
			xi18n.LocalizeWithLangAndTemplateData(req.Lang, &emailContent, i18nMap),
		},
		Actions: []hermes.Action{
			{
				Instructions: "",
				Button: hermes.Button{
					Color: MedicCrewButtonColor,
					Text:  xi18n.LocalizeWithLangAndTemplateData(req.Lang, &emailButtonText, i18nMap),
					Link:  shiftUrl,
				},
			},
		},
	}

	content, err := xhermes.GenerateHTML(HermesDefaultProduct(medicCrewLogoUrl), body)
	if err != nil {
		return err
	}

	err = xmail.SendMail(req.Email, fmt.Sprintf("[%s] %s", xconfig.MailConf.Name, xi18n.LocalizeWithLangAndTemplateData(req.Lang, &emailSubject, i18nMap)), "text/html", content)
	if err != nil {
		return err
	}

	return nil
}

// endregion ---------------------------------------------------- 11. 工作未填滿緊急通知 (facility) ----------------------------------------------------

// region ---------------------------------------------------- 12. 專業人士取消工作通知 (facility) ----------------------------------------------------

// 專業人士取消工作通知請求
type FacilityJobCancellationReq struct {
	Lang             string `json:"lang"`             // 語言
	FacilityName     string `json:"facilityName"`     // 機構名稱
	ProfessionalName string `json:"professionalName"` // 專業人士名稱
	Email            string `json:"email"`            // 機構郵箱
	JobId            uint64 `json:"jobId"`            // 工作ID
}

// 發送取消工作通知 - 通知Facility
func (s *emailNotificationService) SendFacilityJobCancellationNotification(db *gorm.DB, req FacilityJobCancellationReq) error {
	// 獲取機構未填滿工作 URL
	shiftUrl, err := CommonSettingService.GetSettingValueByCode(db, model.CommonSettingCodeFacilityUnfilledJobsUrl)
	if err != nil {
		return err
	}
	jobIdStr := strconv.FormatUint(req.JobId, 10)
	shiftUrl = fmt.Sprintf("%s/%s/applicant", FrontendEncryptService.Encrypt(jobIdStr), shiftUrl)

	i18nMap := map[string]string{
		"FacilityName":     req.FacilityName,
		"ProfessionalName": req.ProfessionalName,
	}

	emailGreeting := i18n.Message{
		ID:    "email.professional_job_cancellation.greeting",
		Other: "Hi {{.FacilityName}}",
	}
	emailSignature := i18n.Message{
		ID:    "email.professional_job_cancellation.signature",
		Other: "Thank you,\nMedic Crew",
	}
	emailSubject := i18n.Message{
		ID:    "email.professional_job_cancellation.subject",
		Other: "Job Cancellation Notification",
	}
	emailContent := i18n.Message{
		ID:    "email.professional_job_cancellation.content",
		Other: "We wish to inform you that {{.ProfessionalName}} has had to cancel their upcoming shift at your facility.\n\nWe understand this may cause inconvenience. You may wish to review other applicants for the job on the platform to find a suitable replacement.",
	}
	emailButtonText := i18n.Message{
		ID:    "email.professional_job_cancellation.button",
		Other: "Click here to view the shift",
	}

	// 構建電郵內容
	body := hermes.Body{
		Greeting:  xi18n.LocalizeWithLangAndTemplateData(req.Lang, &emailGreeting, i18nMap),
		Name:      "",
		Signature: xi18n.LocalizeWithLangAndTemplateData(req.Lang, &emailSignature, i18nMap),
		Intros: []string{
			xi18n.LocalizeWithLangAndTemplateData(req.Lang, &emailContent, i18nMap),
		},
		Actions: []hermes.Action{
			{
				Instructions: "",
				Button: hermes.Button{
					Color: MedicCrewButtonColor,
					Text:  xi18n.LocalizeWithLangAndTemplateData(req.Lang, &emailButtonText, i18nMap),
					Link:  shiftUrl,
				},
			},
		},
	}

	content, err := xhermes.GenerateHTML(HermesDefaultProduct(medicCrewLogoUrl), body)
	if err != nil {
		return err
	}

	err = xmail.SendMail(req.Email, fmt.Sprintf("[%s] %s", xconfig.MailConf.Name, xi18n.LocalizeWithLangAndTemplateData(req.Lang, &emailSubject, i18nMap)), "text/html", content)
	if err != nil {
		return err
	}

	return nil
}

// endregion ---------------------------------------------------- 12. 專業人士取消工作通知 (facility) ----------------------------------------------------

// region ---------------------------------------------------- 13. 機構取消工作通知 (professional) ----------------------------------------------------

// 機構取消工作通知請求
type ProfessionalJobCancellationReq struct {
	Lang             string `json:"lang"`             // 語言
	ProfessionalName string `json:"professionalName"` // 專業人士名稱
	FacilityName     string `json:"facilityName"`     // 機構名稱
	Email            string `json:"email"`            // 專業人士郵箱
}

// 發送取消工作通知 - 通知專業人士
func (s *emailNotificationService) SendProfessionalJobCancellationNotification(db *gorm.DB, req ProfessionalJobCancellationReq) error {
	// 獲取專業人士我的工作 URL
	jobsUrl, err := CommonSettingService.GetSettingValueByCode(db, model.CommonSettingCodeProfessionalMyJobsUrl)
	if err != nil {
		return err
	}

	i18nMap := map[string]string{
		"ProfessionalName": req.ProfessionalName,
		"FacilityName":     req.FacilityName,
	}

	emailGreeting := i18n.Message{
		ID:    "email.facility_job_cancellation.greeting",
		Other: "Hi {{.ProfessionalName}}",
	}
	emailSignature := i18n.Message{
		ID:    "email.facility_job_cancellation.signature",
		Other: "Thank you,\nMedic Crew",
	}
	emailSubject := i18n.Message{
		ID:    "email.facility_job_cancellation.subject",
		Other: "Job Cancellation Notification",
	}
	emailContent := i18n.Message{
		ID:    "email.facility_job_cancellation.content",
		Other: "This is to inform you that the job/shift you were scheduled for at {{.FacilityName}} has been cancelled.\n\nWe apologise for any inconvenience this may cause and thank you for your understanding.\nWe recommend checking for other available jobs on the platform.",
	}
	emailButtonText := i18n.Message{
		ID:    "email.facility_job_cancellation.button",
		Other: "Click here to view jobs",
	}

	// 構建電郵內容
	body := hermes.Body{
		Greeting:  xi18n.LocalizeWithLangAndTemplateData(req.Lang, &emailGreeting, i18nMap),
		Name:      "",
		Signature: xi18n.LocalizeWithLangAndTemplateData(req.Lang, &emailSignature, i18nMap),
		Intros: []string{
			xi18n.LocalizeWithLangAndTemplateData(req.Lang, &emailContent, i18nMap),
		},
		Actions: []hermes.Action{
			{
				Instructions: "",
				Button: hermes.Button{
					Color: MedicCrewButtonColor,
					Text:  xi18n.LocalizeWithLangAndTemplateData(req.Lang, &emailButtonText, i18nMap),
					Link:  jobsUrl,
				},
			},
		},
	}

	content, err := xhermes.GenerateHTML(HermesDefaultProduct(medicCrewLogoUrl), body)
	if err != nil {
		return err
	}

	err = xmail.SendMail(req.Email, fmt.Sprintf("[%s] %s", xconfig.MailConf.Name, xi18n.LocalizeWithLangAndTemplateData(req.Lang, &emailSubject, i18nMap)), "text/html", content)
	if err != nil {
		return err
	}

	return nil
}

// endregion ---------------------------------------------------- 13. 機構取消工作通知 (professional) ----------------------------------------------------

// region ---------------------------------------------------- 批量電郵通知函數 ----------------------------------------------------

// 批量發送新機構申請通知電郵（發送給管理員）
func (s *emailNotificationService) SendNewFacilityApplicationEmailBatch(facilityId uint64, facilityProfileId uint64, lang string) error {
	logger := log.WithFields(log.Fields{
		"emailType":         EmailTypeAdminNewFacilityApplication,
		"facilityId":        facilityId,
		"facilityProfileId": facilityProfileId,
		"lang":              lang,
	})

	// 構建電郵請求
	req := NewFacilityApplicationReq{
		FacilityId:        facilityId,
		FacilityProfileId: facilityProfileId,
		Lang:              lang,
	}

	// 發送到電郵隊列
	err := s.SendEmailTask(EmailTypeAdminNewFacilityApplication, req)
	if err != nil {
		logger.WithError(err).Error("Failed to send new facility application email task")
		return fmt.Errorf("failed to send new facility application email task: %v", err)
	}

	logger.Info("New facility application email task sent successfully")
	return nil
}

// 批量發送協助工單通知電郵（發送給管理員）
// TODO: 暫時不需要處理，預留待處理
func (s *emailNotificationService) SendHelpDeskNotificationEmailBatch(db *gorm.DB, adminReminders []AdminReminderInfo) error {
	logger := log.WithFields(log.Fields{
		"emailType":     EmailTypeAdminHelpDeskNotification,
		"reminderCount": len(adminReminders),
	})

	if len(adminReminders) == 0 {
		logger.Info("No help desk notification reminders to process")
		return nil
	}

	successCount := 0
	errorCount := 0

	for _, reminder := range adminReminders {
		// 構建電郵請求
		req := AdminHelpDeskNotificationReq{
			Lang:          reminder.Lang,
			RequesterName: reminder.RequesterName,
			AdminEmail:    reminder.AdminEmail,
		}

		// 發送到電郵隊列
		err := s.SendEmailTask(EmailTypeAdminHelpDeskNotification, req)
		if err != nil {
			logger.WithError(err).WithFields(log.Fields{
				"adminEmail":    reminder.AdminEmail,
				"requesterName": reminder.RequesterName,
			}).Error("Failed to send help desk notification email")
			errorCount++
		} else {
			successCount++
		}
	}

	logger.WithFields(log.Fields{
		"successCount": successCount,
		"errorCount":   errorCount,
		"totalCount":   len(adminReminders),
	}).Info("Batch help desk notification email sending completed")

	if errorCount > 0 {
		return fmt.Errorf("failed to send %d out of %d help desk notification emails", errorCount, len(adminReminders))
	}

	return nil
}

// 批量發送協議準備完成通知電郵（發送給機構）
// TODO: 暫時不需要處理，預留待處理
func (s *emailNotificationService) SendAgreementReadyEmailBatch(db *gorm.DB, agreementReminders []AgreementReminderInfo) error {
	logger := log.WithFields(log.Fields{
		"emailType":     EmailTypeFacilityAgreementReady,
		"reminderCount": len(agreementReminders),
	})

	if len(agreementReminders) == 0 {
		logger.Info("No agreement ready reminders to process")
		return nil
	}

	successCount := 0
	errorCount := 0

	for _, reminder := range agreementReminders {
		// 構建電郵請求
		req := FacilityAgreementReadyReq{
			Lang:         reminder.Lang,
			FacilityName: reminder.FacilityName,
			Email:        reminder.Email,
		}

		// 發送到電郵隊列
		err := s.SendEmailTask(EmailTypeFacilityAgreementReady, req)
		if err != nil {
			logger.WithError(err).WithFields(log.Fields{
				"facilityId": reminder.FacilityId,
				"email":      reminder.Email,
			}).Error("Failed to send agreement ready email")
			errorCount++
		} else {
			successCount++
		}
	}

	logger.WithFields(log.Fields{
		"successCount": successCount,
		"errorCount":   errorCount,
		"totalCount":   len(agreementReminders),
	}).Info("Batch agreement ready email sending completed")

	if errorCount > 0 {
		return fmt.Errorf("failed to send %d out of %d agreement ready emails", errorCount, len(agreementReminders))
	}

	return nil
}

// 批量發送協議續期提醒電郵（發送給管理員）
func (s *emailNotificationService) SendAgreementRenewalReminderEmailBatch(adminReminders []AdminReminderInfo) error {
	logger := log.WithFields(log.Fields{
		"emailType":     EmailTypeFacilityAgreementRenewalReminder,
		"reminderCount": len(adminReminders),
	})

	if len(adminReminders) == 0 {
		logger.Info("No agreement renewal reminder reminders to process")
		return nil
	}

	successCount := 0
	errorCount := 0

	for _, reminder := range adminReminders {
		// 構建電郵請求
		req := FacilityAgreementRenewalReminderReq{
			Lang:         reminder.Lang,
			FacilityName: reminder.FacilityName,
			AdminEmail:   reminder.AdminEmail,
		}

		// 發送到電郵隊列
		err := s.SendEmailTask(EmailTypeFacilityAgreementRenewalReminder, req)
		if err != nil {
			logger.WithError(err).WithFields(log.Fields{
				"adminEmail":   reminder.AdminEmail,
				"facilityName": reminder.FacilityName,
			}).Error("Failed to send agreement renewal reminder email")
			errorCount++
		} else {
			successCount++
		}
	}

	logger.WithFields(log.Fields{
		"successCount": successCount,
		"errorCount":   errorCount,
		"totalCount":   len(adminReminders),
	}).Info("Batch agreement renewal reminder email sending completed")

	if errorCount > 0 {
		return fmt.Errorf("failed to send %d out of %d agreement renewal reminder emails", errorCount, len(adminReminders))
	}

	return nil
}

// 批量發送協議過期通知電郵（發送給機構）
// TODO: 暫時不需要處理，預留待處理
func (s *emailNotificationService) SendAgreementExpiredEmailBatch(db *gorm.DB, agreementReminders []AgreementReminderInfo) error {
	logger := log.WithFields(log.Fields{
		"emailType":     EmailTypeFacilityAgreementExpired,
		"reminderCount": len(agreementReminders),
	})

	if len(agreementReminders) == 0 {
		logger.Info("No agreement expired reminders to process")
		return nil
	}

	successCount := 0
	errorCount := 0

	for _, reminder := range agreementReminders {
		// 構建電郵請求
		req := FacilityAgreementExpiredReq{
			Lang:         reminder.Lang,
			FacilityName: reminder.FacilityName,
			Email:        reminder.Email,
		}

		// 發送到電郵隊列
		err := s.SendEmailTask(EmailTypeFacilityAgreementExpired, req)
		if err != nil {
			logger.WithError(err).WithFields(log.Fields{
				"facilityId": reminder.FacilityId,
				"email":      reminder.Email,
			}).Error("Failed to send agreement expired email")
			errorCount++
		} else {
			successCount++
		}
	}

	logger.WithFields(log.Fields{
		"successCount": successCount,
		"errorCount":   errorCount,
		"totalCount":   len(agreementReminders),
	}).Info("Batch agreement expired email sending completed")

	if errorCount > 0 {
		return fmt.Errorf("failed to send %d out of %d agreement expired emails", errorCount, len(agreementReminders))
	}

	return nil
}

// endregion ---------------------------------------------------- 批量電郵通知函數 ----------------------------------------------------

// region ---------------------------------------------------- Simplified Email Task Parameters 簡化電郵任務參數 ----------------------------------------------------

// 簡化的工作取消電郵任務參數
type JobCancellationEmailTaskReq struct {
	JobId              uint64 `json:"jobId"` // 工作ID
	JobApplicationId   uint64 `json:"jobApplicationId"`
	ProfessionalUserId uint64 `json:"professionalUserId"` // 專業人士用戶ID
	Lang               string `json:"lang"`               // 語言
}

// 簡化的審核結果電郵任務參數
type ApprovalEmailTaskReq struct {
	UserId            uint64 `json:"userId"`            // 用戶ID
	ProfessionalId    uint64 `json:"professionalId"`    // 用戶ID
	FacilityId        uint64 `json:"facilityId"`        // 機構ID (機構審核時使用)
	FacilityProfileId uint64 `json:"facilityProfileId"` // 機構資料ID (機構審核時使用)
	Lang              string `json:"lang"`              // 語言
}

// 簡化的發票通知電郵任務參數
type InvoiceEmailTaskReq struct {
	JobApplicationId uint64 `json:"jobApplicationId"` // 工作申請ID
	DocumentId       uint64 `json:"documentId"`       // 單據ID
	Lang             string `json:"lang"`             // 語言
}

// 簡化的緊急工作未填滿電郵任務參數
type UrgentShiftEmailTaskReq struct {
	JobId uint64 `json:"jobId"` // 工作ID
	Lang  string `json:"lang"`  // 語言
}

// endregion ---------------------------------------------------- Simplified Email Task Parameters 簡化電郵任務參數 ----------------------------------------------------

// 處理專業人士取消工作通知任務 - 通知機構
func ProcessFacilityJobCancellation(db *gorm.DB, data interface{}) error {
	dataBytes, err := json.Marshal(data)
	if err != nil {
		return err
	}

	var req JobCancellationEmailTaskReq
	err = json.Unmarshal(dataBytes, &req)
	if err != nil {
		return err
	}

	// 數據查詢
	var job model.Job
	var jobApplication model.JobApplication
	var professional model.Professional
	var professionalUser xmodel.User
	var facility model.Facility
	var facilityProfile model.FacilityProfile

	err = db.Where("id = ?", req.JobId).First(&job).Error
	if err != nil {
		return fmt.Errorf("failed to query job: %v", err)
	}
	err = db.Where("id = ?", req.JobApplicationId).First(&jobApplication).Error
	if err != nil {
		return fmt.Errorf("failed to query job: %v", err)
	}

	err = db.Where("user_id = ? AND id = ?", req.ProfessionalUserId, jobApplication.ProfessionalId).First(&professional).Error
	if err != nil {
		return fmt.Errorf("failed to query professional: %v", err)
	}

	err = db.Where("id = ?", req.ProfessionalUserId).First(&professionalUser).Error
	if err != nil {
		return fmt.Errorf("failed to query professional user: %v", err)
	}

	err = db.Where("id = ?", job.FacilityId).First(&facility).Error
	if err != nil {
		return fmt.Errorf("failed to query facility: %v", err)
	}

	err = db.Where("facility_id = ?", job.FacilityId).First(&facilityProfile).Error
	if err != nil {
		return fmt.Errorf("failed to query facility profile: %v", err)
	}

	facilityUserIds, err := SystemNotificationService.GetFacilityUserIdsByJob(db, model.SystemNotificationTypeFacilityJobCancelled, req.JobId)
	if err != nil {
		return fmt.Errorf("failed to query facility user ids: %v", err)
	}
	for _, facilityUserId := range facilityUserIds {
		var user xmodel.User
		if err = db.Where("id = ? AND user_type = ?", facilityUserId, model.UserUserTypeFacilityUser).First(&user).Error; err != nil {
			return fmt.Errorf("failed to query user: %v", err)
		}

		if err = EmailNotificationService.SendFacilityJobCancellationNotification(db, FacilityJobCancellationReq{
			Lang:             req.Lang,
			FacilityName:     facilityProfile.Name,
			ProfessionalName: professional.FirstName + " " + professional.LastName,
			Email:            user.Email,
			JobId:            req.JobId,
		}); err != nil {
			// 忽略錯誤
			log.WithFields(log.Fields{
				"jobId":              req.JobId,
				"jobApplicationId":   req.JobApplicationId,
				"professionalUserId": req.ProfessionalUserId,
			}).WithError(err).Error("Failed to send facility job cancellation email")
		}
	}

	return nil
}

// 處理取消工作通知任務 - 通知專業人士
func ProcessProfessionalJobCancellation(db *gorm.DB, data interface{}) error {
	dataBytes, err := json.Marshal(data)
	if err != nil {
		return err
	}

	var req JobCancellationEmailTaskReq
	err = json.Unmarshal(dataBytes, &req)
	if err != nil {
		return err
	}

	var job model.Job
	var jobApplication model.JobApplication
	var professional model.Professional
	var professionalUser xmodel.User
	var facilityProfile model.FacilityProfile

	err = db.Where("id = ?", req.JobId).First(&job).Error
	if err != nil {
		return fmt.Errorf("failed to query job: %v", err)
	}

	err = db.Where("id = ? AND user_id = ?", req.JobApplicationId, req.ProfessionalUserId).First(&jobApplication).Error
	if err != nil {
		return fmt.Errorf("failed to query job: %v", err)
	}

	err = db.Where("user_id = ? AND id = ?", req.ProfessionalUserId, jobApplication.ProfessionalId).First(&professional).Error
	if err != nil {
		return fmt.Errorf("failed to query professional: %v", err)
	}

	err = db.Where("id = ? AND user_type = ?", req.ProfessionalUserId, model.UserUserTypeProfessional).First(&professionalUser).Error
	if err != nil {
		return fmt.Errorf("failed to query professional user: %v", err)
	}

	err = db.Where("facility_id = ? AND id = ?", job.FacilityId, job.FacilityProfileId).First(&facilityProfile).Error
	if err != nil {
		return fmt.Errorf("failed to query facility profile: %v", err)
	}

	// 構建完整的電郵請求
	emailReq := ProfessionalJobCancellationReq{
		Lang:             req.Lang,
		ProfessionalName: professional.FirstName + " " + professional.LastName,
		FacilityName:     facilityProfile.Name,
		Email:            professionalUser.Email,
	}

	return EmailNotificationService.SendProfessionalJobCancellationNotification(db, emailReq)
}

// 處理專業人士審核通過通知任務 - 通知專業人士
func ProcessProfessionalApproved(db *gorm.DB, data interface{}) error {
	dataBytes, err := json.Marshal(data)
	if err != nil {
		return err
	}

	var req ApprovalEmailTaskReq
	err = json.Unmarshal(dataBytes, &req)
	if err != nil {
		return err
	}

	// 查詢專業人士信息
	var user xmodel.User
	var professional model.Professional

	err = db.Where("id = ?", req.UserId).First(&user).Error
	if err != nil {
		return fmt.Errorf("failed to query user: %v", err)
	}

	err = db.Where("user_id = ?", req.UserId).First(&professional).Error
	if err != nil {
		return fmt.Errorf("failed to query professional: %v", err)
	}

	// 構建電郵請求
	emailReq := ProfessionalApprovedReq{
		Lang:             req.Lang,
		ProfessionalName: professional.FirstName + " " + professional.LastName,
		Email:            user.Email,
	}

	return EmailNotificationService.SendProfessionalApprovedNotification(db, emailReq)
}

// 處理專業人士資料審核拒絕通知任務
func ProcessProfessionalRejected(db *gorm.DB, data interface{}) error {
	dataBytes, err := json.Marshal(data)
	if err != nil {
		return err
	}

	var req ApprovalEmailTaskReq
	err = json.Unmarshal(dataBytes, &req)
	if err != nil {
		return err
	}

	// 查詢專業人士信息
	var user xmodel.User
	var professional model.Professional

	err = db.Where("id = ?", req.UserId).First(&user).Error
	if err != nil {
		return fmt.Errorf("failed to query user: %v", err)
	}

	err = db.Where("user_id = ?", req.UserId).First(&professional).Error
	if err != nil {
		return fmt.Errorf("failed to query professional: %v", err)
	}

	// 構建電郵請求
	emailReq := ProfessionalRejectedReq{
		Lang:             req.Lang,
		ProfessionalName: professional.FirstName + " " + professional.LastName,
		Email:            user.Email,
	}

	return EmailNotificationService.SendProfessionalRejectedNotification(db, emailReq)
}

// 處理發票通知任務
func ProcessFacilityInvoiceNotification(db *gorm.DB, data interface{}) error {
	dataBytes, err := json.Marshal(data)
	if err != nil {
		return err
	}

	var req InvoiceEmailTaskReq
	err = json.Unmarshal(dataBytes, &req)
	if err != nil {
		return err
	}

	// 查詢工作申請和相關信息
	var jobApplication model.JobApplication
	var job model.Job
	var facility model.Facility
	var facilityProfile model.FacilityProfile
	var facilityUser model.FacilityUser
	var user xmodel.User

	err = db.Where("id = ?", req.JobApplicationId).First(&jobApplication).Error
	if err != nil {
		return fmt.Errorf("failed to query job application: %v", err)
	}

	err = db.Where("id = ?", jobApplication.JobId).First(&job).Error
	if err != nil {
		return fmt.Errorf("failed to query job: %v", err)
	}

	err = db.Where("id = ?", job.FacilityId).First(&facility).Error
	if err != nil {
		return fmt.Errorf("failed to query facility: %v", err)
	}

	err = db.Where("facility_id = ?", job.FacilityId).First(&facilityProfile).Error
	if err != nil {
		return fmt.Errorf("failed to query facility profile: %v", err)
	}

	err = db.Where("facility_id = ? AND primary_user = ?", job.FacilityId, model.FacilityUserPrimaryUserY).First(&facilityUser).Error
	if err != nil {
		return fmt.Errorf("failed to query facility user: %v", err)
	}

	err = db.Where("id = ?", facilityUser.UserId).First(&user).Error
	if err != nil {
		return fmt.Errorf("failed to query user: %v", err)
	}

	// 構建電郵請求
	emailReq := FacilityInvoiceNotificationReq{
		Lang:         req.Lang,
		FacilityName: facilityProfile.Name,
		Email:        user.Email,
		DocumentId:   req.DocumentId,
	}

	return EmailNotificationService.SendFacilityInvoiceNotification(db, emailReq)
}

// 處理緊急工作未填滿通知任務 - 通知機構
func ProcessFacilityUrgentShiftUnfilled(db *gorm.DB, data interface{}) error {
	dataBytes, err := json.Marshal(data)
	if err != nil {
		return err
	}

	var req UrgentShiftEmailTaskReq
	err = json.Unmarshal(dataBytes, &req)
	if err != nil {
		return err
	}

	// 查詢工作和機構信息
	var job model.Job
	var facility model.Facility
	var facilityProfile model.FacilityProfile
	var facilityUser model.FacilityUser
	var user xmodel.User

	err = db.Where("id = ?", req.JobId).First(&job).Error
	if err != nil {
		return fmt.Errorf("failed to query job: %v", err)
	}

	err = db.Where("id = ?", job.FacilityId).First(&facility).Error
	if err != nil {
		return fmt.Errorf("failed to query facility: %v", err)
	}

	err = db.Where("facility_id = ?", job.FacilityId).First(&facilityProfile).Error
	if err != nil {
		return fmt.Errorf("failed to query facility profile: %v", err)
	}

	err = db.Where("facility_id = ? AND primary_user = ?", job.FacilityId, model.FacilityUserPrimaryUserY).First(&facilityUser).Error
	if err != nil {
		return fmt.Errorf("failed to query facility user: %v", err)
	}

	err = db.Where("id = ?", facilityUser.UserId).First(&user).Error
	if err != nil {
		return fmt.Errorf("failed to query user: %v", err)
	}

	// 構建電郵請求
	emailReq := FacilityUrgentShiftUnfilledReq{
		Lang:         req.Lang,
		FacilityName: facilityProfile.Name,
		Email:        user.Email,
	}

	return EmailNotificationService.SendFacilityUrgentShiftUnfilledNotification(db, emailReq)
}
