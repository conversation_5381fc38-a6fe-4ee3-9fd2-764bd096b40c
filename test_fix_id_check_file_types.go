// 測試腳本：修復專業人士 ID Check 文件類型
// 使用方法：go run test_fix_id_check_file_types.go

package main

import (
	"fmt"
	"log"
	"strings"

	"github.com/Norray/medic-crew/model"
	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xconfig"
	"github.com/Norray/xrocket/xgorm"
	"gorm.io/gorm"
)

// UserProfessionalInfo 用戶專業人士信息
type UserProfessionalInfo struct {
	UserId              uint64 `json:"userId"`
	ApprovedId          uint64 `json:"approvedId"`
	DraftId             uint64 `json:"draftId"`
	HasDraft            bool   `json:"hasDraft"`
	ApprovedProfileJson string `json:"approvedProfileJson"`
	DraftProfileJson    string `json:"draftProfileJson"`
}

// professionalProfileIdCheckFileMap ID Check 文件映射
var professionalProfileIdCheckFileMap = map[string]string{
	model.ProfessionalFileCodeAustralianPassport:                    "PRIMARY_FILE",
	model.ProfessionalFileCodeForeignPassport:                       "PRIMARY_FILE",
	model.ProfessionalFileCodeAustralianBirthCertificate:            "PRIMARY_FILE",
	model.ProfessionalFileCodeAustralianCitizenshipCertificate:      "PRIMARY_FILE",
	model.ProfessionalFileCodeCurrentAustraliaDriverLicence:         "SECONDARY_FILE",
	model.ProfessionalFileCodeAustralianPublicServiceEmployeeIDCard: "SECONDARY_FILE",
	model.ProfessionalFileCodeOtherAustralianGovernmentIssueIDCard:  "SECONDARY_FILE",
	model.ProfessionalFileCodeTertiaryStudentIDCard:                 "SECONDARY_FILE",
	model.ProfessionalFileCodeCreditDebitAtmCard:                    "OTHER_FILE",
	model.ProfessionalFileCodeMedicareCard:                          "OTHER_FILE",
	model.ProfessionalFileCodeUtilityBillOrRateNotice:               "OTHER_FILE",
	model.ProfessionalFileCodeStatementFromFinancialInstitution:     "OTHER_FILE",
	model.ProfessionalFileCodeCentrelinkOrPensionCard:               "OTHER_FILE",
}

func main() {
	fmt.Println("=== 專業人士 ID Check 文件類型修復工具 ===\n")

	// 初始化配置
	xconfig.Setup("./config/app.ini")
	xgorm.DefaultSetup()

	db := xgorm.DB
	if db == nil {
		log.Fatal("❌ 無法連接到數據庫")
	}

	fmt.Println("✅ 數據庫連接成功")

	// 1. 查詢統計信息
	fmt.Println("\n📊 統計信息:")
	queryStatistics(db)

	// 2. 查詢需要處理的用戶
	usersWithApproved, err := queryUsersWithApprovedProfessionals(db)
	if err != nil {
		log.Fatalf("❌ 查詢用戶失敗: %v", err)
	}

	if len(usersWithApproved) == 0 {
		fmt.Println("ℹ️  沒有需要處理的用戶")
		return
	}

	fmt.Printf("\n📋 找到 %d 個需要處理的用戶\n", len(usersWithApproved))

	// 3. 顯示前幾個用戶的詳情
	fmt.Println("\n📝 前 5 個用戶詳情:")
	displayUserDetails(db, usersWithApproved[:min(5, len(usersWithApproved))])

	// 4. 確認是否繼續
	fmt.Print("\n❓ 是否繼續處理所有用戶？(y/N): ")
	var confirm string
	fmt.Scanln(&confirm)
	if confirm != "y" && confirm != "Y" {
		fmt.Println("❌ 用戶取消操作")
		return
	}

	// 5. 開始處理
	fmt.Println("\n🚀 開始處理...")
	processAllUsers(db, usersWithApproved)
}

// queryStatistics 查詢統計信息
func queryStatistics(db *gorm.DB) {
	// 統計已通過的專業人士數量
	var approvedCount int64
	err := db.Model(&model.Professional{}).
		Where("data_type = ?", model.ProfessionalDataTypeApproved).
		Count(&approvedCount).Error
	if err != nil {
		fmt.Printf("❌ 查詢已通過專業人士數量失敗: %v\n", err)
	} else {
		fmt.Printf("  📝 已通過的專業人士: %d 個\n", approvedCount)
	}

	// 統計有草稿版本的專業人士數量
	var draftCount int64
	err = db.Model(&model.Professional{}).
		Where("data_type = ?", model.ProfessionalDataTypeDraft).
		Count(&draftCount).Error
	if err != nil {
		fmt.Printf("❌ 查詢草稿專業人士數量失敗: %v\n", err)
	} else {
		fmt.Printf("  📄 草稿版本的專業人士: %d 個\n", draftCount)
	}

	// 統計同時有兩個版本的用戶數量
	var bothVersionsCount int64
	query := `
		SELECT COUNT(DISTINCT approved.user_id)
		FROM professional approved
		INNER JOIN professional draft ON draft.user_id = approved.user_id
		WHERE approved.data_type = ? AND draft.data_type = ?
	`
	err = db.Raw(query, model.ProfessionalDataTypeApproved, model.ProfessionalDataTypeDraft).Scan(&bothVersionsCount).Error
	if err != nil {
		fmt.Printf("❌ 查詢同時有兩個版本的用戶數量失敗: %v\n", err)
	} else {
		fmt.Printf("  🔄 同時有兩個版本的用戶: %d 個\n", bothVersionsCount)
	}
}

// queryUsersWithApprovedProfessionals 查詢有 APPROVED 版本的用戶
func queryUsersWithApprovedProfessionals(db *gorm.DB) ([]UserProfessionalInfo, error) {
	var results []UserProfessionalInfo

	query := `
		SELECT 
			approved.user_id,
			approved.id as approved_id,
			approved.profile_json as approved_profile_json,
			COALESCE(draft.id, 0) as draft_id,
			CASE WHEN draft.id IS NOT NULL THEN 1 ELSE 0 END as has_draft,
			COALESCE(draft.profile_json, '') as draft_profile_json
		FROM professional approved
		LEFT JOIN professional draft ON draft.user_id = approved.user_id AND draft.data_type = ?
		WHERE approved.data_type = ?
		ORDER BY approved.user_id
		LIMIT 100
	`

	err := db.Raw(query, model.ProfessionalDataTypeDraft, model.ProfessionalDataTypeApproved).Scan(&results).Error
	return results, err
}

// displayUserDetails 顯示用戶詳情
func displayUserDetails(db *gorm.DB, users []UserProfessionalInfo) {
	for i, user := range users {
		fmt.Printf("\n  %d. 用戶 ID: %d\n", i+1, user.UserId)
		fmt.Printf("     已通過版本 ID: %d\n", user.ApprovedId)
		if user.HasDraft {
			fmt.Printf("     草稿版本 ID: %d\n", user.DraftId)
		} else {
			fmt.Printf("     草稿版本: 無\n")
		}

		// 分析 APPROVED 版本的 IdCheckFileTypes
		analyzeIdCheckFileTypes(user.ApprovedProfileJson, "APPROVED")

		if user.HasDraft && user.DraftProfileJson != "" {
			analyzeIdCheckFileTypes(user.DraftProfileJson, "DRAFT")
		}
	}
}

// analyzeIdCheckFileTypes 分析 IdCheckFileTypes
func analyzeIdCheckFileTypes(profileJson, version string) {
	// 這裡簡化處理，實際應該解析 JSON
	if strings.Contains(profileJson, `"idCheckFileTypes":""`) || !strings.Contains(profileJson, `"idCheckFileTypes"`) {
		fmt.Printf("     %s IdCheckFileTypes: 空\n", version)
	} else {
		fmt.Printf("     %s IdCheckFileTypes: 有值\n", version)
	}
}

// processAllUsers 處理所有用戶
func processAllUsers(db *gorm.DB, users []UserProfessionalInfo) {
	successCount := 0
	errorCount := 0

	for i, user := range users {
		fmt.Printf("\n[%d/%d] 處理用戶 ID: %d\n", i+1, len(users), user.UserId)

		err := processUserIdCheckFiles(db, user)
		if err != nil {
			fmt.Printf("❌ 處理失敗: %v\n", err)
			errorCount++
		} else {
			fmt.Printf("✅ 處理成功\n")
			successCount++
		}
	}

	fmt.Printf("\n=== 處理完成 ===\n")
	fmt.Printf("📊 統計結果:\n")
	fmt.Printf("  ✅ 成功: %d\n", successCount)
	fmt.Printf("  ❌ 失敗: %d\n", errorCount)
	fmt.Printf("  📝 總計: %d\n", len(users))
}

// processUserIdCheckFiles 處理用戶的 ID Check 文件
func processUserIdCheckFiles(db *gorm.DB, userInfo UserProfessionalInfo) error {
	// 開始事務
	tx := db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 1. 處理 APPROVED 版本
	idCheckFileTypes, err := processApprovedVersion(tx, userInfo.ApprovedId)
	if err != nil {
		tx.Rollback()
		return fmt.Errorf("處理 APPROVED 版本失敗: %v", err)
	}

	// 2. 處理 DRAFT 版本（如果存在）
	if userInfo.HasDraft {
		err = processDraftVersion(tx, userInfo.DraftId, idCheckFileTypes)
		if err != nil {
			tx.Rollback()
			return fmt.Errorf("處理 DRAFT 版本失敗: %v", err)
		}
	}

	// 提交事務
	return tx.Commit().Error
}

// processApprovedVersion 處理 APPROVED 版本
func processApprovedVersion(tx *gorm.DB, approvedId uint64) (string, error) {
	var professional model.Professional
	err := tx.Where("id = ?", approvedId).First(&professional).Error
	if err != nil {
		return "", err
	}

	profile, err := professional.UnmarshalProfile(professional.ProfileJson)
	if err != nil {
		return "", err
	}

	// 處理 IdCheckFileTypes
	idCheckFileTypes := processIdCheckFileTypes(&profile)

	// 更新並保存
	profile.IdCheckFileTypes = idCheckFileTypes
	err = professional.MarshalProfile(profile)
	if err != nil {
		return "", err
	}

	err = tx.Save(&professional).Error
	return idCheckFileTypes, err
}

// processIdCheckFileTypes 處理 IdCheckFileTypes 字段
func processIdCheckFileTypes(profile *model.ProfessionalProfile) string {
	if profile.IdCheckFileTypes == "" {
		fmt.Printf("    情況1: 從 Files 中提取 ID Check 文件類型\n")
		
		var idCheckFileCodes []string
		var filesToRemove []int

		for i, file := range profile.Files {
			if _, isIdCheckFile := professionalProfileIdCheckFileMap[file.FileCode]; isIdCheckFile {
				if len(file.ProfessionalFileIds) > 0 {
					idCheckFileCodes = append(idCheckFileCodes, file.FileCode)
					filesToRemove = append(filesToRemove, i)
				}
			}
		}

		// 去重
		idCheckFileCodes = removeDuplicateStrings(idCheckFileCodes)

		// 刪除文件
		for i := len(filesToRemove) - 1; i >= 0; i-- {
			index := filesToRemove[i]
			profile.Files = append(profile.Files[:index], profile.Files[index+1:]...)
		}

		return strings.Join(idCheckFileCodes, ",")
	} else {
		fmt.Printf("    情況2: IdCheckFileTypes 已有值: %s\n", profile.IdCheckFileTypes)
		return profile.IdCheckFileTypes
	}
}

// processDraftVersion 處理 DRAFT 版本
func processDraftVersion(tx *gorm.DB, draftId uint64, idCheckFileTypes string) error {
	var professional model.Professional
	err := tx.Where("id = ?", draftId).First(&professional).Error
	if err != nil {
		return err
	}

	profile, err := professional.UnmarshalProfile(professional.ProfileJson)
	if err != nil {
		return err
	}

	// 設置 IdCheckFileTypes
	profile.IdCheckFileTypes = idCheckFileTypes

	// 刪除所有 ID Check 文件
	var updatedFiles []model.ProfessionalProfileFile
	for _, file := range profile.Files {
		if _, isIdCheckFile := professionalProfileIdCheckFileMap[file.FileCode]; !isIdCheckFile {
			updatedFiles = append(updatedFiles, file)
		}
	}
	profile.Files = updatedFiles

	// 保存
	err = professional.MarshalProfile(profile)
	if err != nil {
		return err
	}

	return tx.Save(&professional).Error
}

// removeDuplicateStrings 去重
func removeDuplicateStrings(slice []string) []string {
	keys := make(map[string]bool)
	var result []string
	for _, item := range slice {
		if !keys[item] {
			keys[item] = true
			result = append(result, item)
		}
	}
	return result
}

// min 返回較小值
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
