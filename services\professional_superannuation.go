package services

import (
	"time"

	"github.com/Norray/medic-crew/model"
	"github.com/Norray/xrocket/xgorm"
	"github.com/Norray/xrocket/xtool"
	"github.com/jinzhu/copier"
	"github.com/nicksnyder/go-i18n/v2/i18n"
	"gorm.io/gorm"
)

var ProfessionalSuperannuationService = new(professionalSuperannuationService)

type professionalSuperannuationService struct{}

// CheckIdExist 检查养老金信息记录是否存在
func (s *professionalSuperannuationService) CheckIdExist(db *gorm.DB, m *model.ProfessionalSuperannuation, id uint64) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.professional_superannuation.id.does_not_exist",
		Other: "No such record, please try after reloading.",
	}
	var err error
	if err = db.First(&m, id).Error; xgorm.IsSqlErr(err) {
		return false, msg, err
	}
	if xgorm.IsNotFoundErr(err) {
		return false, msg, nil
	}
	return true, msg, nil
}

// CheckDeclarationConfirmed 检查当DataType为SUBMITTED时，DeclarationConfirmed必须为Y
func (s *professionalSuperannuationService) CheckDeclarationConfirmed(req ProfessionalSuperannuationEditReq) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.professional_superannuation.declaration.not_confirmed",
		Other: "Declaration must be confirmed when submitting superannuation information.",
	}

	if req.DataType == model.ProfessionalSuperannuationDataTypeSubmitted && req.DeclarationConfirmed != "Y" {
		return false, msg, nil
	}

	return true, msg, nil
}

// 养老金信息编辑请求
type ProfessionalSuperannuationEditReq struct {
	ProfessionalSuperannuationId uint64 `json:"professionalSuperannuationId" binding:"required"`                                                             // 养老金信息ID
	DataType                     string `json:"dataType" binding:"required,oneof=DRAFT SUBMITTED"`                                                           // 数据类型，可选值：DRAFT、SUBMITTED
	SuperannuationType           string `json:"superannuationType" binding:"required,oneof=EXISTING_SUPER_FUND DEFAULT_SUPER_FUND PRIVATE_SMSF"`             // 養老金类型，可选值：EXISTING_SUPER_FUND、DEFAULT_SUPER_FUND、PRIVATE_SMSF
	FullName                     string `json:"fullName" binding:"required_if=DataType SUBMITTED,omitempty,max=255"`                                         // 个人全名
	TaxFileNumber                string `json:"taxFileNumber" binding:"required_if=DataType SUBMITTED,omitempty,max=255"`                                    // 税务文件号码
	SuperFundName                string `json:"superFundName" binding:"required_if=SuperannuationType EXISTING_SUPER_FUND,omitempty,max=255"`                // 养老金基金名称
	SuperFundAbn                 string `json:"superFundAbn" binding:"required_if=SuperannuationType EXISTING_SUPER_FUND,omitempty,max=255"`                 // 养老金基金ABN号码(Australian Business Number)
	SuperFundUsi                 string `json:"superFundUsi" binding:"required_if=SuperannuationType EXISTING_SUPER_FUND,omitempty,max=255"`                 // 养老金基金USI号码(Unique Superannuation Identifier)
	SuperFundMemberAccountNumber string `json:"superFundMemberAccountNumber" binding:"required_if=SuperannuationType EXISTING_SUPER_FUND,omitempty,max=255"` // 会员账户号码
	SuperFundAccountName         string `json:"superFundAccountName" binding:"required_if=SuperannuationType EXISTING_SUPER_FUND,omitempty,max=255"`         // 账户名称
	SmsfName                     string `json:"smsfName" binding:"required_if=SuperannuationType PRIVATE_SMSF,omitempty,max=255"`                            // 自管理养老金名称(Self-Managed Super Fund)
	SmsfAbn                      string `json:"smsfAbn" binding:"required_if=SuperannuationType PRIVATE_SMSF,omitempty,max=255"`                             // 自管理养老金ABN号码
	SmsfElectronicServiceAddress string `json:"smsfElectronicServiceAddress" binding:"required_if=SuperannuationType PRIVATE_SMSF,omitempty,max=255"`        // 自管理养老金电子服务地址
	SmsfAccountName              string `json:"smsfAccountName" binding:"required_if=SuperannuationType PRIVATE_SMSF,omitempty,max=255"`                     // 自管理养老金账户名称
	SmsfBankAccountName          string `json:"smsfBankAccountName" binding:"required_if=SuperannuationType PRIVATE_SMSF,omitempty,max=255"`                 // 自管理养老金银行账户名称
	BsbCode                      string `json:"bsbCode" binding:"required_if=SuperannuationType PRIVATE_SMSF,omitempty,max=255"`                             // 银行BSB代码(Bank State Branch)
	SmsfBankAccountNumber        string `json:"smsfBankAccountNumber" binding:"required_if=SuperannuationType PRIVATE_SMSF,omitempty,max=255"`               // 自管理养老金银行账户号码
	DeclarationConfirmed         string `json:"declarationConfirmed" binding:"required_if=DataType SUBMITTED,omitempty,len=1,oneof=Y N"`                     // 声明 Y/N
}

type ProfessionalSuperannuationEditDraftReq struct {
	ProfessionalSuperannuationId uint64 `json:"professionalSuperannuationId" binding:"required"`                                                 // 养老金信息ID
	DataType                     string `json:"dataType" binding:"required,oneof=DRAFT SUBMITTED"`                                               // 数据类型，可选值：DRAFT、SUBMITTED
	SuperannuationType           string `json:"superannuationType" binding:"required,oneof=EXISTING_SUPER_FUND DEFAULT_SUPER_FUND PRIVATE_SMSF"` // 養老金类型，可选值：EXISTING_SUPER_FUND、DEFAULT_SUPER_FUND、PRIVATE_SMSF
	FullName                     string `json:"fullName" binding:"omitempty,max=255"`                                                            // 个人全名
	TaxFileNumber                string `json:"taxFileNumber" binding:"omitempty,max=255"`                                                       // 税务文件号码
	SuperFundName                string `json:"superFundName" binding:"omitempty,max=255"`                                                       // 养老金基金名称
	SuperFundAbn                 string `json:"superFundAbn" binding:"omitempty,max=255"`                                                        // 养老金基金ABN号码(Australian Business Number)
	SuperFundUsi                 string `json:"superFundUsi" binding:"omitempty,max=255"`                                                        // 养老金基金USI号码(Unique Superannuation Identifier)
	SuperFundMemberAccountNumber string `json:"superFundMemberAccountNumber" binding:"omitempty,max=255"`                                        // 会员账户号码
	SuperFundAccountName         string `json:"superFundAccountName" binding:"omitempty,max=255"`                                                // 账户名称
	SmsfName                     string `json:"smsfName" binding:"omitempty,max=255"`                                                            // 自管理养老金名称(Self-Managed Super Fund)
	SmsfAbn                      string `json:"smsfAbn" binding:"omitempty,max=255"`                                                             // 自管理养老金ABN号码
	SmsfElectronicServiceAddress string `json:"smsfElectronicServiceAddress" binding:"omitempty,max=255"`                                        // 自管理养老金电子服务地址
	SmsfAccountName              string `json:"smsfAccountName" binding:"omitempty,max=255"`                                                     // 自管理养老金账户名称
	SmsfBankAccountName          string `json:"smsfBankAccountName" binding:"omitempty,max=255"`                                                 // 自管理养老金银行账户名称
	BsbCode                      string `json:"bsbCode" binding:"omitempty,max=255"`                                                             // 银行BSB代码(Bank State Branch)
	SmsfBankAccountNumber        string `json:"smsfBankAccountNumber" binding:"omitempty,max=255"`                                               // 自管理养老金银行账户号码
	DeclarationConfirmed         string `json:"declarationConfirmed" binding:"omitempty,len=1,oneof=Y N"`                                        // 声明 Y/N
}

// Edit 更新养老金信息
func (s *professionalSuperannuationService) Edit(db *gorm.DB, req ProfessionalSuperannuationEditReq, userId uint64) error {
	var err error
	var m model.ProfessionalSuperannuation
	if err = db.First(&m, req.ProfessionalSuperannuationId).Error; err != nil {
		return err
	}

	_ = xtool.CopyWithIgnoreFieldNames(&m, &req, "Id", "UserId", "DataType", "SubmittedTime")
	if err = db.Save(&m).Error; err != nil {
		return err
	}

	if req.DataType == model.ProfessionalSuperannuationDataTypeSubmitted {
		var submit model.ProfessionalSuperannuation
		if err = db.Where("user_id = ? AND data_type = ?", userId, model.ProfessionalSuperannuationDataTypeSubmitted).First(&submit).Error; err != nil {
			return err
		}

		_ = xtool.CopyWithIgnoreFieldNames(&submit, &req, "Id", "UserId", "DataType")
		nowTime := time.Now().UTC()
		submit.SubmittedTime = &nowTime
		if err = db.Save(&submit).Error; err != nil {
			return err
		}
	}

	return nil
}

type SuperannuationInquireReq struct {
	UserId   uint64 `form:"userId" binding:"required"`
	DataType string `form:"-"`
}

type ProfessionalSuperannuationInquireReq struct {
	UserId   uint64 `form:"-"`
	DataType string `form:"-"`
}

type ProfessionalSuperannuationInquireResp struct {
	Id                           uint64     `json:"professionalSuperannuationId"`
	UserId                       uint64     `json:"userId"`
	DataType                     string     `json:"dataType"`
	SuperannuationType           string     `json:"superannuationType"`
	FullName                     string     `json:"fullName"`
	TaxFileNumber                string     `json:"taxFileNumber"`
	SuperFundName                string     `json:"superFundName"`
	SuperFundAbn                 string     `json:"superFundAbn"`
	SuperFundUsi                 string     `json:"superFundUsi"`
	SuperFundMemberAccountNumber string     `json:"superFundMemberAccountNumber"`
	SuperFundAccountName         string     `json:"superFundAccountName"`
	SmsfName                     string     `json:"smsfName"`
	SmsfAbn                      string     `json:"smsfAbn"`
	SmsfElectronicServiceAddress string     `json:"smsfElectronicServiceAddress"`
	SmsfAccountName              string     `json:"smsfAccountName"`
	SmsfBankAccountName          string     `json:"smsfBankAccountName"`
	BsbCode                      string     `json:"bsbCode"`
	SmsfBankAccountNumber        string     `json:"smsfBankAccountNumber"`
	DeclarationConfirmed         string     `json:"declarationConfirmed"`
	SubmittedTime                *time.Time `json:"submittedTime"`
}

func (s *professionalSuperannuationService) Inquire(db *gorm.DB, req ProfessionalSuperannuationInquireReq) (ProfessionalSuperannuationInquireResp, error) {
	var err error
	var resp ProfessionalSuperannuationInquireResp
	var m model.ProfessionalSuperannuation
	if err = db.First(&m, "user_id = ? AND data_type = ?", req.UserId, req.DataType).Error; err != nil {
		return resp, err
	}
	_ = copier.Copy(&resp, m)
	return resp, nil
}
