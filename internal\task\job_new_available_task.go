package task

import (
	"context"
	"encoding/json"
	"fmt"
	"math"
	"strings"
	"time"

	"github.com/Norray/medic-crew/model"
	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xgorm"
	uuid "github.com/satori/go.uuid"
	"github.com/shopspring/decimal"
	log "github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

const JobNewAvailableTask = "job_new_available_task" // rabbitmq

type JobNewAvailableReq struct {
	JobId uint64 `json:"jobId"` // 工作ID
}

// 執行隊列 - 新工作可用通知
func JobNewAvailable(taskJson string) (context.Context, error) {
	var err error
	ctx := context.Background()
	traceId := uuid.NewV4().String()
	ctx = context.WithValue(ctx, "traceId", traceId)

	logger := log.WithField("traceId", traceId).WithField("task", JobNewAvailableTask)

	taskData := JobNewAvailableReq{}
	err = json.Unmarshal([]byte(taskJson), &taskData)
	if err != nil {
		logger.Errorf("fail to unmarshal task: %v, taskJson: %s", err, taskJson)
		return ctx, err
	}
	logger = logger.WithField("JobId", taskData.JobId)
	logger.Info("Start to process job new available notification")

	// 獲取數據庫連接
	db := xgorm.DB.WithContext(ctx)

	// 獲取工作信息
	var job model.Job
	if err = db.Where("id = ?", taskData.JobId).First(&job).Error; err != nil {
		logger.Errorf("fail to get job: %v", err)
		return ctx, err
	}

	// 檢查工作狀態是否為發佈中
	if job.Status != model.JobStatusPublish {
		logger.Warnf("job status is not publish: %s", job.Status)
		return ctx, nil
	}

	// 查找符合條件的專業人士
	professionalUserIds, err := findMatchingProfessionals(db, job, logger)
	if err != nil {
		logger.Errorf("fail to find matching professionals: %v", err)
		return ctx, err
	}

	if len(professionalUserIds) == 0 {
		logger.Info("no matching professionals found")
		return ctx, nil
	}

	logger.Infof("found %d matching professionals", len(professionalUserIds))

	// 在事務中批量發送通知
	successCount := 0
	errorCount := 0

	err = db.Transaction(func(tx *gorm.DB) error {
		for _, professionalUserId := range professionalUserIds {
			notificationReq := services.CreateProfessionalJobNewAvailableReq{
				ProfessionalUserId: professionalUserId,
				JobId:              job.Id,
				StartTime:          job.BeginTime.Format(time.RFC3339),
			}

			if err := services.SystemNotificationService.CreateProfessionalJobNewAvailable(tx, notificationReq); err != nil {
				logger.Errorf("fail to create notification for professional %d: %v", professionalUserId, err)
				errorCount++
				// 繼續處理其他通知，不回滾事務
				continue
			}
			successCount++
		}
		return nil
	})

	if err != nil {
		logger.Errorf("transaction failed for job new available notifications: %v", err)
		return ctx, err
	}

	logger.Infof("job new available notification completed: success=%d, error=%d", successCount, errorCount)
	return ctx, nil
}

// 查找符合條件的專業人士
func findMatchingProfessionals(db *gorm.DB, job model.Job, logger *log.Entry) ([]uint64, error) {

	// 獲取服務地點信息
	var serviceLocation model.ServiceLocation
	if err := db.Where("id = ?", job.ServiceLocationId).First(&serviceLocation).Error; err != nil {
		return nil, fmt.Errorf("fail to get service location: %v", err)
	}

	// 基礎查詢條件
	builder := db.Table("professional AS p").
		Select("DISTINCT p.user_id").
		Joins("JOIN user AS u ON p.user_id = u.id").
		Where("p.data_type = ?", model.ProfessionalDataTypeApproved).
		Where("p.profession = ?", job.PositionProfession)

	// 排除已申請此工作的專業人士
	builder = builder.Joins("LEFT JOIN job_application AS ja ON p.user_id = ja.user_id AND ja.job_id = ? AND ja.deleted <> ?",
		job.Id, model.JobApplicationDeletedY).
		Where("ja.id IS NULL")

	// 排除被機構拉黑的專業人士
	builder = builder.Joins("LEFT JOIN facility_blacklist AS fb ON p.user_id = fb.user_id AND fb.facility_id = ?", job.FacilityId).
		Where("fb.id IS NULL")

	// 根據職業類型添加特定條件
	switch job.PositionProfession {
	case model.ProfessionalProfessionEnrolledNurse:
		// 檢查藥劑師認證要求
		if job.MedicationEndorsed == model.JobMedicationEndorsedY {
			builder = builder.Where("p.medication_endorsement = ?", model.JobMedicationEndorsedY)
		}
	}

	// 專業領域匹配
	if job.Specialisation != "" {
		if job.PositionProfession == model.ProfessionalProfessionMedicalPractitioner {
			builder = builder.Where("JSON_SEARCH(p.profile_json, 'one', ?, NULL, '$.preferredSpecialities[*].subSpeciality') IS NOT NULL", job.Specialisation)
		} else {
			builder = builder.Where("JSON_SEARCH(p.profile_json, 'one', ?, NULL, '$.preferredSpecialities[*].speciality') IS NOT NULL", job.Specialisation)
		}
	}

	// 添加最低時薪篩選條件
	if !job.MinHourlyRate.IsZero() {
		builder = builder.Where("(p.minimum_hourly_rate IS NULL OR p.minimum_hourly_rate = 0 OR p.minimum_hourly_rate <= ?)", job.MinHourlyRate)
	}

	// 查詢基礎匹配的專業人士
	type ProfessionalResult struct {
		UserId            uint64          `json:"userId"`
		ProfileJson       string          `json:"profileJson"`
		ExperienceLevel   string          `json:"experienceLevel"`
		LocationLat       decimal.Decimal `json:"locationLat"`
		LocationLng       decimal.Decimal `json:"locationLng"`
		DistanceWithin    decimal.Decimal `json:"distanceWithin"`
		MinimumHourlyRate decimal.Decimal `json:"minimumHourlyRate"`
		PreferredState    string          `json:"preferredState"`
		PreferredLocality string          `json:"preferredLocality"`
		Language          string          `json:"language"`
	}
	var professionals []ProfessionalResult

	if err := builder.Select([]string{
		"p.user_id",
		"p.profile_json",
		"p.experience_level",
		"p.location_lat",
		"p.location_lng",
		"p.distance_within",
		"p.minimum_hourly_rate",
		"p.preferred_state",
		"p.preferred_locality",
		"p.language",
	}).Find(&professionals).Error; err != nil {
		return nil, fmt.Errorf("fail to query matching professionals: %v", err)
	}

	// 獲取等級信息
	var gradeToSeq map[string]int32
	var levelToSeq map[string]int32

	// 如果需要檢查醫生專業等級
	if job.PositionProfession == model.ProfessionalProfessionMedicalPractitioner &&
		job.Specialisation != "" && job.PreferredGrade != "" {
		gradeToSeq = getGradeMapping(db, logger)
	}

	// 如果需要檢查經驗等級
	if job.MinExperienceLevel != "" {
		levelToSeq = getExperienceLevelMapping(db, job.PositionProfession, logger)
	}

	// 進一步檢查等級和經驗等級匹配
	filteredUserIds := make([]uint64, 0)
	for _, prof := range professionals {
		// 檢查醫生的專業等級匹配
		if job.PositionProfession == model.ProfessionalProfessionMedicalPractitioner &&
			job.Specialisation != "" && job.PreferredGrade != "" {
			// 解析 ProfileJson 獲取專業領域信息
			var profile struct {
				PreferredSpecialities []struct {
					SubSpeciality string `json:"subSpeciality"`
					Grade         string `json:"grade"`
				} `json:"preferredSpecialities"`
			}

			if err := json.Unmarshal([]byte(prof.ProfileJson), &profile); err != nil {
				logger.Errorf("fail to unmarshal profile json for user %d: %v", prof.UserId, err)
				continue
			}

			if !checkGradeMatchWithMapping(profile.PreferredSpecialities, job.Specialisation, job.PreferredGrade, gradeToSeq, logger) {
				continue
			}
		}

		// 檢查經驗等級匹配（使用 Professional 表中的 ExperienceLevel）
		if job.MinExperienceLevel != "" {
			if !checkExperienceLevelMatchWithMapping(prof.ExperienceLevel, job.MinExperienceLevel, levelToSeq, logger) {
				continue
			}
		}

		lat, _ := prof.LocationLat.Float64()
		lng, _ := prof.LocationLng.Float64()
		distanceWithin, _ := prof.DistanceWithin.Float64()
		// 檢查地理位置匹配（距離或偏好位置）
		if !checkLocationOrPreferenceMatch(lat, lng, distanceWithin, prof.PreferredState, prof.PreferredLocality, serviceLocation, logger) {
			continue
		}

		// 檢查語言匹配
		if !checkLanguageMatch(prof.Language, job.Language, job.LanguageRequirement, logger) {
			continue
		}

		// 檢查工作可用性設置
		if !checkJobAvailabilityUsingService(db, prof.UserId, job.Id, logger) {
			continue
		}

		// 檢查專業人士在該時間段是否有時間衝突
		hasConflict, _, err := services.JobService.CheckProfessionalTimeConflict(db, prof.UserId, job.Id)
		if err != nil {
			logger.Errorf("fail to check time conflict for user %d: %v", prof.UserId, err)
			continue
		}
		if hasConflict {
			logger.Debugf("user %d has time conflict for job %d", prof.UserId, job.Id)
			continue
		}

		filteredUserIds = append(filteredUserIds, prof.UserId)
	}

	return filteredUserIds, nil
}

// 獲取等級映射（一次性查詢）
func getGradeMapping(db *gorm.DB, logger *log.Entry) map[string]int32 {
	var grades []model.Selection
	if err := db.Where("selection_type = ?", model.SelectionTypePreferredGrade).
		Order("seq ASC").
		Find(&grades).Error; err != nil {
		logger.Errorf("fail to get grade selections: %v", err)
		return make(map[string]int32)
	}

	gradeToSeq := make(map[string]int32)
	for _, grade := range grades {
		gradeToSeq[grade.Code] = grade.Seq
	}
	return gradeToSeq
}

// 獲取經驗等級映射（一次性查詢）
func getExperienceLevelMapping(db *gorm.DB, profession string, logger *log.Entry) map[string]int32 {
	// 根據職業類型確定對應的經驗等級選項類型
	var selectionType string
	switch profession {
	case model.ProfessionalProfessionMedicalPractitioner:
		selectionType = model.SelectionTypeExperienceLevelMedicalPractitioner
	case model.ProfessionalProfessionRegisteredNurse:
		selectionType = model.SelectionTypeExperienceLevelRegisteredNurse
	case model.ProfessionalProfessionEnrolledNurse:
		selectionType = model.SelectionTypeExperienceLevelGeneral
	case model.ProfessionalProfessionPersonalCareWorker:
		selectionType = model.SelectionTypeExperienceLevelGeneral
	default:
		logger.Warnf("unknown profession for experience level mapping: %s", profession)
		return make(map[string]int32)
	}

	var experienceLevels []model.Selection
	if err := db.Where("selection_type = ?", selectionType).
		Order("seq ASC").
		Find(&experienceLevels).Error; err != nil {
		logger.Errorf("fail to get experience level selections: %v", err)
		return make(map[string]int32)
	}

	levelToSeq := make(map[string]int32)
	for _, level := range experienceLevels {
		levelToSeq[level.Code] = level.Seq
	}
	return levelToSeq
}

// 檢查醫生等級是否匹配
func checkGradeMatchWithMapping(preferredSpecialities []struct {
	SubSpeciality string `json:"subSpeciality"`
	Grade         string `json:"grade"`
}, jobSpecialisation, jobPreferredGrade string, gradeToSeq map[string]int32, logger *log.Entry) bool {
	// 查找匹配的專業領域
	for _, speciality := range preferredSpecialities {
		if speciality.SubSpeciality == jobSpecialisation {
			// 檢查等級是否符合要求
			return checkGradeHierarchyWithMapping(speciality.Grade, jobPreferredGrade, gradeToSeq, logger)
		}
	}
	return false
}

// 檢查經驗等級是否匹配
func checkExperienceLevelMatchWithMapping(experienceLevel, jobMinExperienceLevel string, levelToSeq map[string]int32, logger *log.Entry) bool {
	if experienceLevel == "" {
		return false
	}

	professionalSeq, professionalExists := levelToSeq[experienceLevel]
	jobSeq, jobExists := levelToSeq[jobMinExperienceLevel]

	if !professionalExists || !jobExists {
		logger.Warnf("experience level not found in mapping: professional=%s, job=%s", experienceLevel, jobMinExperienceLevel)
		return false
	}

	return professionalSeq >= jobSeq
}

// 檢查等級層次匹配
func checkGradeHierarchyWithMapping(professionalGrade, jobRequiredGrade string, gradeToSeq map[string]int32, logger *log.Entry) bool {
	professionalSeq, professionalExists := gradeToSeq[professionalGrade]
	jobSeq, jobExists := gradeToSeq[jobRequiredGrade]

	if !professionalExists || !jobExists {
		logger.Warnf("grade not found in mapping: professional=%s, job=%s", professionalGrade, jobRequiredGrade)
		return false
	}

	return professionalSeq >= jobSeq
}

// 檢查地理位置或偏好位置是否匹配
func checkLocationOrPreferenceMatch(profLat, profLng float64, profDistanceWithin float64, profPreferredState, profPreferredLocality string, serviceLocation model.ServiceLocation, logger *log.Entry) bool {
	if profDistanceWithin == 0 {
		return true
	}
	// 方案1：檢查距離匹配
	if checkDistanceMatch(profLat, profLng, profDistanceWithin, serviceLocation.LocationLat, serviceLocation.LocationLng, logger) {
		return true
	}

	// 方案2：檢查偏好州和城市匹配
	if checkPreferredLocationMatch(profPreferredState, profPreferredLocality, serviceLocation.LocationState, serviceLocation.LocationCity, logger) {
		return true
	}

	return false
}

// 檢查距離是否匹配
func checkDistanceMatch(profLat, profLng float64, profDistanceWithin float64, jobLat, jobLng decimal.Decimal, logger *log.Entry) bool {
	// 如果專業人士沒有設置位置信息，不進行距離檢查
	if profLat == 0 && profLng == 0 {
		return false
	}

	// 如果工作沒有位置信息，不進行距離檢查
	if jobLat.IsZero() && jobLng.IsZero() {
		return false
	}

	// 如果專業人士沒有設置距離限制，不進行距離檢查
	if profDistanceWithin <= 0 {
		return false
	}

	// 轉換工作位置為 float64
	jobLatFloat, _ := jobLat.Float64()
	jobLngFloat, _ := jobLng.Float64()

	// 計算兩點間的距離（公里）
	distance := calculateDistance(profLat, profLng, jobLatFloat, jobLngFloat)

	// 檢查距離是否在專業人士設定的範圍內
	return distance <= profDistanceWithin
}

// 檢查偏好位置是否匹配
func checkPreferredLocationMatch(profPreferredState, profPreferredLocality, jobLocationState, jobLocationCity string, logger *log.Entry) bool {
	// 如果專業人士沒有設置偏好州，不進行偏好位置檢查
	if profPreferredState == "" {
		return false
	}

	// 州必須完全匹配
	if profPreferredState != jobLocationState {
		return false
	}

	// 如果專業人士沒有設置偏好城市，只要州匹配就通過
	if profPreferredLocality == "" {
		return true
	}

	// 城市也必須完全匹配
	return profPreferredLocality == jobLocationCity
}

// 檢查語言是否匹配
func checkLanguageMatch(profLanguage, jobLanguage, jobLanguageRequirement string, logger *log.Entry) bool {
	// 如果工作不要求語言，直接通過
	if jobLanguageRequirement == "N" {
		return true
	}

	// 如果專業人士沒有設置語言，不匹配
	if profLanguage == "" {
		return false
	}

	// 如果工作沒有設置語言要求，直接通過
	if jobLanguage == "" {
		return true
	}

	// 解析專業人士的語言列表
	profLangs := strings.Split(profLanguage, ",")
	profLangSet := make(map[string]struct{}, len(profLangs))
	for _, lang := range profLangs {
		profLangSet[strings.TrimSpace(lang)] = struct{}{}
	}

	// 解析工作要求的語言列表
	jobLangs := strings.Split(jobLanguage, ",")
	for i := range jobLangs {
		jobLangs[i] = strings.TrimSpace(jobLangs[i])
	}

	// 檢查專業人士是否掌握工作要求的所有語言
	for _, lang := range jobLangs {
		if _, ok := profLangSet[lang]; !ok {
			return false
		}
	}

	return true
}

// 使用現有 service 檢查工作可用性
func checkJobAvailabilityUsingService(db *gorm.DB, userId, jobId uint64, logger *log.Entry) bool {
	// 使用 JobService.CheckIdExistForProfessional 來檢查專業人士是否可以看到這個工作
	// 這個方法包含了完整的可用性檢查邏輯
	exists, _, err := services.JobService.CheckIdExistForProfessional(db, jobId, userId)
	if err != nil {
		logger.Errorf("fail to check job availability for user %d and job %d: %v", userId, jobId, err)
		return true // 如果檢查失敗，不阻止通知
	}
	return exists
}

// 計算兩個地理坐標點之間的距離（使用 Haversine 公式）
func calculateDistance(lat1, lng1, lat2, lng2 float64) float64 {
	const earthRadius = 6371 // 地球半徑（公里）

	// 轉換為弧度
	lat1Rad := lat1 * math.Pi / 180
	lng1Rad := lng1 * math.Pi / 180
	lat2Rad := lat2 * math.Pi / 180
	lng2Rad := lng2 * math.Pi / 180

	// 計算差值
	deltaLat := lat2Rad - lat1Rad
	deltaLng := lng2Rad - lng1Rad

	// Haversine 公式
	a := math.Sin(deltaLat/2)*math.Sin(deltaLat/2) +
		math.Cos(lat1Rad)*math.Cos(lat2Rad)*
			math.Sin(deltaLng/2)*math.Sin(deltaLng/2)
	c := 2 * math.Atan2(math.Sqrt(a), math.Sqrt(1-a))

	// 計算距離
	distance := earthRadius * c
	return distance
}
