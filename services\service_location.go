package services

import (
	"fmt"
	"time"

	"github.com/Norray/medic-crew/model"
	"github.com/Norray/xrocket/xapp/xresp"
	"github.com/Norray/xrocket/xgorm"
	"github.com/jinzhu/copier"
	"github.com/nicksnyder/go-i18n/v2/i18n"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

var ServiceLocationService = new(serviceLocationService)

type serviceLocationService struct{}

func (s *serviceLocationService) CheckIdExist(db *gorm.DB, m *model.ServiceLocation, id uint64, facilityId ...uint64) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.service_location.id.does_not_exist",
		Other: "No such record, please try after reloading.",
	}
	var err error
	builder := db.Model(&model.ServiceLocation{}).Where("id = ?", id)
	if len(facilityId) > 0 {
		builder = builder.Where("facility_id = ?", facilityId[0])
	}

	if err = builder.First(&m).Error; xgorm.IsSqlErr(err) {
		return false, msg, err
	}
	if xgorm.IsNotFoundErr(err) {
		return false, msg, nil
	} else {
		return true, msg, nil
	}
}

type ServiceLocationCreateReq struct {
	FacilityId    uint64          `json:"facilityId" binding:"required"`
	Name          string          `json:"name" binding:"required"`
	Address       string          `json:"address" binding:"required"`
	AddressExtra  string          `json:"addressExtra"`
	LocationState string          `json:"locationState" binding:"required"`
	LocationCity  string          `json:"locationCity"  binding:"required"`
	LocationRoute string          `json:"locationRoute"`
	LocationLat   decimal.Decimal `json:"locationLat"`
	LocationLng   decimal.Decimal `json:"locationLng"`
	Status        string          `json:"status" binding:"required,oneof=ENABLE DISABLE"`
	Timezone      string          `json:"timezone" binding:"required,timezone"`
}

type ServiceLocationCreateResp struct {
	ServiceLocationId uint64 `json:"serviceLocationId"`
}

func (s *serviceLocationService) Create(db *gorm.DB, req ServiceLocationCreateReq) (ServiceLocationCreateResp, error) {
	var resp ServiceLocationCreateResp
	var err error
	var m model.ServiceLocation
	_ = copier.Copy(&m, req)
	if m.Timezone != "" {
		m.TimezoneOffset, err = s.GetTimeZoneOffset(m.Timezone)
		if err != nil {
			return resp, err
		}
	} else {
		m.Timezone = ""
		m.TimezoneOffset = ""
	}
	if err = db.Create(&m).Error; err != nil {
		return resp, err
	}
	resp.ServiceLocationId = m.Id
	return resp, nil
}

type ServiceLocationListReq struct {
	FacilityId    uint64 `form:"facilityId"  binding:"required"`
	Address       string `form:"address"`
	AddressExtra  string `form:"addressExtra"`
	LocationState string `form:"locationState"`
	LocationCity  string `form:"locationCity"`
	LocationRoute string `form:"locationRoute"`
	Status        string `form:"status"`
	SearchContent string `form:"searchContent"`
}

type ServiceLocationListResp struct {
	ServiceLocationId uint64          `json:"serviceLocationId"`
	FacilityId        uint64          `json:"facilityId"`
	Name              string          `json:"name"`
	Address           string          `json:"address"`
	AddressExtra      string          `json:"addressExtra"`
	LocationState     string          `json:"locationState"`
	LocationCity      string          `json:"locationCity"`
	LocationRoute     string          `json:"locationRoute"`
	LocationLat       decimal.Decimal `json:"locationLat"`
	LocationLng       decimal.Decimal `json:"locationLng"`
	Status            string          `json:"status"`
	Timezone          string          `json:"timezone"`
}

func (s *serviceLocationService) List(db *gorm.DB, req ServiceLocationListReq, pageSet *xresp.PageSet) ([]ServiceLocationListResp, error) {
	var err error
	var resp []ServiceLocationListResp
	builder := db.Table("service_location AS sl").Select([]string{
		"sl.id AS service_location_id",
		"sl.facility_id",
		"sl.name",
		"sl.address",
		"sl.address_extra",
		"sl.location_state",
		"sl.location_city",
		"sl.location_route",
		"sl.location_lat",
		"sl.location_lng",
		"sl.status",
		"sl.timezone",
	}).Where("sl.facility_id = ?", req.FacilityId)

	if req.Address != "" {
		builder = builder.Where("sl.address LIKE ?", xgorm.EscapeLikeWithWildcards(req.Address))
	}
	if req.AddressExtra != "" {
		builder = builder.Where("sl.address_extra LIKE ?", xgorm.EscapeLikeWithWildcards(req.AddressExtra))
	}
	if req.LocationState != "" {
		builder = builder.Where("sl.location_state LIKE ?", xgorm.EscapeLikeWithWildcards(req.LocationState))
	}
	if req.LocationCity != "" {
		builder = builder.Where("sl.location_city LIKE ?", xgorm.EscapeLikeWithWildcards(req.LocationCity))
	}
	if req.LocationRoute != "" {
		builder = builder.Where("sl.location_route LIKE ?", xgorm.EscapeLikeWithWildcards(req.LocationRoute))
	}
	if req.Status != "" {
		builder = builder.Where("sl.status = ?", req.Status)
	}
	if req.SearchContent != "" {
		builder = builder.Where("sl.name LIKE ? OR CONCAT_WS(' ', sl.address, sl.address_extra) LIKE ?", xgorm.EscapeLikeWithWildcards(req.SearchContent), xgorm.EscapeLikeWithWildcards(req.SearchContent), xgorm.EscapeLikeWithWildcards(req.SearchContent), xgorm.EscapeLikeWithWildcards(req.SearchContent), xgorm.EscapeLikeWithWildcards(req.SearchContent), xgorm.EscapeLikeWithWildcards(req.SearchContent))
	}
	if err = builder.Scopes(xresp.Paginate(pageSet)).Order("sl.id").Find(&resp).Error; err != nil {
		return resp, err
	}
	return resp, nil
}

type ServiceLocationSearchReq struct {
	FacilityId    uint64 `form:"facilityId" binding:"required"`
	SearchContent string `form:"searchContent"`
	Name          string `form:"name"`
	Address       string `form:"address"`
	AddressExtra  string `form:"addressExtra"`
	LocationState string `form:"locationState"`
	LocationCity  string `form:"locationCity"`
	LocationRoute string `form:"locationRoute"`
	Status        string `form:"status" binding:"required,oneof=ENABLE DISABLE"`
	SelectedId    uint64 `form:"selectedId"`
	Limit         int    `form:"limit"`
}

type ServiceLocationSearchResp struct {
	ServiceLocationId uint64          `json:"serviceLocationId"`
	FacilityId        uint64          `json:"facilityId"`
	Name              string          `json:"name"`
	Address           string          `json:"address"`
	AddressExtra      string          `json:"addressExtra"`
	LocationState     string          `json:"locationState"`
	LocationCity      string          `json:"locationCity"`
	LocationRoute     string          `json:"locationRoute"`
	LocationLat       decimal.Decimal `json:"locationLat"`
	LocationLng       decimal.Decimal `json:"locationLng"`
	Status            string          `json:"status"`
	Timezone          string          `json:"timezone"`
}

func (s *serviceLocationService) Search(db *gorm.DB, req ServiceLocationSearchReq) ([]ServiceLocationSearchResp, error) {
	var err error
	var resp []ServiceLocationSearchResp
	builder := db.Table("service_location AS sl").Select([]string{
		"sl.id AS service_location_id",
		"sl.facility_id",
		"sl.name",
		"sl.address",
		"sl.address_extra",
		"sl.location_state",
		"sl.location_city",
		"sl.location_route",
		"sl.location_lat",
		"sl.location_lng",
		"sl.status",
		"sl.timezone",
	}).Where("sl.facility_id = ?", req.FacilityId)

	if req.Name != "" {
		builder = builder.Where("sl.name LIKE ?", "%"+req.Name+"%")
	}
	if req.Address != "" {
		builder = builder.Where("sl.address LIKE ?", xgorm.EscapeLikeWithWildcards(req.Address))
	}
	if req.AddressExtra != "" {
		builder = builder.Where("sl.address_extra LIKE ?", xgorm.EscapeLikeWithWildcards(req.AddressExtra))
	}
	if req.LocationState != "" {
		builder = builder.Where("sl.location_state LIKE ?", xgorm.EscapeLikeWithWildcards(req.LocationState))
	}
	if req.LocationCity != "" {
		builder = builder.Where("sl.location_city LIKE ?", xgorm.EscapeLikeWithWildcards(req.LocationCity))
	}
	if req.LocationRoute != "" {
		builder = builder.Where("sl.location_route LIKE ?", xgorm.EscapeLikeWithWildcards(req.LocationRoute))
	}
	if req.Status != "" {
		builder = builder.Where("sl.status = ?", req.Status)
	}
	if req.SearchContent != "" {
		builder = builder.Where("sl.name LIKE ? OR CONCAT_WS(' ', sl.address, sl.address_extra) LIKE ?", xgorm.EscapeLikeWithWildcards(req.SearchContent), xgorm.EscapeLikeWithWildcards(req.SearchContent), xgorm.EscapeLikeWithWildcards(req.SearchContent), xgorm.EscapeLikeWithWildcards(req.SearchContent), xgorm.EscapeLikeWithWildcards(req.SearchContent), xgorm.EscapeLikeWithWildcards(req.SearchContent))
	}
	if req.SelectedId != 0 {
		builder = builder.Order(fmt.Sprintf("IF(sl.id = %d,0,1)", req.SelectedId))
	}
	if req.Limit > 0 {
		builder = builder.Limit(req.Limit)
	}
	if err = builder.Order("sl.id").Find(&resp).Error; err != nil {
		return resp, err
	}
	return resp, nil
}

type ServiceLocationEditReq struct {
	ServiceLocationId uint64          `json:"serviceLocationId"  binding:"required"`
	Name              string          `json:"name"  binding:"required"`
	Address           string          `json:"address"  binding:"required"`
	AddressExtra      string          `json:"addressExtra"`
	LocationState     string          `json:"locationState"  binding:"required"`
	LocationCity      string          `json:"locationCity"  binding:"required"`
	LocationRoute     string          `json:"locationRoute"`
	LocationLat       decimal.Decimal `json:"locationLat"`
	LocationLng       decimal.Decimal `json:"locationLng"`
	Status            string          `json:"status" binding:"required,oneof=ENABLE DISABLE"`
	Timezone          string          `json:"timezone" binding:"required,timezone"`
}

func (s *serviceLocationService) Edit(db *gorm.DB, req ServiceLocationEditReq) error {
	var err error
	var m model.ServiceLocation
	if err = db.First(&m, req.ServiceLocationId).Error; err != nil {
		return err
	}
	_ = copier.Copy(&m, req)
	if m.Timezone != "" {
		m.TimezoneOffset, err = s.GetTimeZoneOffset(m.Timezone)
		if err != nil {
			return err
		}
	} else {
		m.Timezone = ""
		m.TimezoneOffset = ""
	}
	if err = db.Save(&m).Error; err != nil {
		return err
	}
	return nil
}

type ServiceLocationInquireReq struct {
	ServiceLocationId uint64 `form:"serviceLocationId" binding:"required"`
}

type ServiceLocationInquireResp struct {
	ServiceLocationId uint64          `json:"serviceLocationId"`
	FacilityId        uint64          `json:"facilityId"`
	Name              string          `json:"name"`
	Address           string          `json:"address"`
	AddressExtra      string          `json:"addressExtra"`
	LocationState     string          `json:"locationState"`
	LocationCity      string          `json:"locationCity"`
	LocationRoute     string          `json:"locationRoute"`
	LocationLat       decimal.Decimal `json:"locationLat"`
	LocationLng       decimal.Decimal `json:"locationLng"`
	Status            string          `json:"status"`
	Timezone          string          `json:"timezone"`
}

func (s *serviceLocationService) Inquire(db *gorm.DB, req ServiceLocationInquireReq) (ServiceLocationInquireResp, error) {
	var err error
	var resp ServiceLocationInquireResp
	var m model.ServiceLocation
	if err = db.First(&m, req.ServiceLocationId).Error; err != nil {
		return resp, err
	}
	_ = copier.Copy(&resp, m)
	resp.ServiceLocationId = m.Id
	return resp, nil
}

type ServiceLocationDeleteReq struct {
	ServiceLocationId uint64 `json:"serviceLocationId"  binding:"required"`
}

func (s *serviceLocationService) Delete(db *gorm.DB, req ServiceLocationDeleteReq) error {
	var err error
	if err = db.Delete(&model.ServiceLocation{}, req.ServiceLocationId).Error; err != nil {
		return err
	}
	return nil
}

// 根據時區取得偏移量(±HH:MM)
func (s *serviceLocationService) GetTimeZoneOffset(timezone string) (string, error) {
	loc, err := time.LoadLocation(timezone)
	if err != nil {
		return "", err
	}

	// 使用當前時間計算時區偏移量
	now := time.Now().In(loc)
	_, offset := now.Zone()

	// 將秒轉換為小時和分鐘
	hours := offset / 3600
	minutes := (offset % 3600) / 60

	// 格式化為 ±HH:MM
	var sign string
	if hours >= 0 {
		sign = "+"
	} else {
		sign = "-"
		hours = -hours
		minutes = -minutes
	}

	return fmt.Sprintf("%s%02d:%02d", sign, hours, minutes), nil
}
