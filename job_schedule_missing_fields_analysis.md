# JobScheduleInquireResp 缺少字段分析報告

## 概述
通過對比 `services/job_schedule.go` 的 `JobScheduleInquireResp` 和 `services/job.go` 的 `JobInquireResp`，發現 JobScheduleInquireResp 缺少 24 個字段。

## 統計信息
- **JobInquireResp 總字段數**：53
- **JobScheduleInquireResp 總字段數**：48
- **共同字段數**：29
- **缺少字段數**：24
- **獨有字段數**：19

## 缺少的字段詳細分析

### 🔴 高優先級缺少字段（建議添加）

#### 1. 用戶相關字段
- **`FacilityName`** (string) - 所屬機構名稱
  - 重要性：高 - 用於顯示機構名稱，提升用戶體驗
- **`CreatedUserId`** (uint64) - 創建者Id
  - 重要性：中 - 用於追蹤創建者信息
- **`UpdatedUserId`** (uint64) - 更新者Id
  - 重要性：中 - 用於追蹤更新者信息

#### 2. 工作標識字段
- **`JobNo`** (string) - 工作編號
  - 重要性：高 - 工作的唯一標識，用於顯示和查詢

#### 3. 名稱字段（用於顯示）
- **`PositionProfessionName`** (string) - 職位專業名稱
- **`MinExperienceLevelName`** (string) - 最低職級要求名稱
- **`PreferredGradeName`** (string) - 首選級別名稱
- **`QualificationName`** (string) - 護理資格名稱
- **`SpecialisationName`** (string) - 專業要求名稱
- **`LanguageName`** (string) - 語言要求名稱
- **`SupervisionLevelName`** (string) - 監督級別名稱
- **`BenefitsName`** (string) - 福利名稱
  - 重要性：高 - 這些名稱字段用於前端顯示，提升用戶體驗

#### 4. 薪資相關字段
- **`MinHourlyRate`** (decimal.Decimal) - 最低時薪
- **`MaxHourlyRate`** (decimal.Decimal) - 最高時薪
  - 重要性：高 - 薪資信息對工作排程很重要

### 🟡 中優先級缺少字段

#### 5. 統計相關字段
- **`AcceptedCount`** (int32) - 已取錄人數
- **`ApplicantCount`** (int32) - 申請人數
  - 重要性：中 - 用於顯示招聘進度

#### 6. 進度和狀態字段
- **`Progress`** (string) - 進度狀態
- **`JobCategory`** (string) - 工作分類
  - 重要性：中 - 用於狀態管理和分類

#### 7. 發佈相關字段
- **`PublishTime`** (*time.Time) - 發佈時間
- **`PublishNow`** (string) - 是否立即發佈
  - 重要性：中 - 用於發佈管理

### 🟢 低優先級缺少字段

#### 8. 其他字段
- **`CancelReason`** (string) - 取消原因
- **`PaymentTerms`** (string) - 付款條件
- **`SplitType`** (string) - 分拆類型
- **`SaveFileInProfile`** (string) - 是否保存文件到機構配置
  - 重要性：低 - 特定場景使用

## 建議的修改方案

### 方案 1：添加所有重要字段
```go
type JobScheduleInquireResp struct {
    // 現有字段...
    
    // 新增高優先級字段
    FacilityName           string          `json:"facilityName"`           // 所屬機構名稱
    CreatedUserId          uint64          `json:"createdUserId"`          // 創建者Id
    UpdatedUserId          uint64          `json:"updatedUserId"`          // 更新者Id
    JobNo                  string          `json:"jobNo"`                  // 工作編號
    PositionProfessionName string          `json:"positionProfessionName"` // 職位專業名稱
    MinExperienceLevelName string          `json:"minExperienceLevelName"` // 最低職級要求名稱
    PreferredGradeName     string          `json:"preferredGradeName"`     // 首選級別名稱
    QualificationName      string          `json:"qualificationName"`      // 護理資格名稱
    SpecialisationName     string          `json:"specialisationName"`     // 專業要求名稱
    LanguageName           string          `json:"languageName"`           // 語言要求名稱
    SupervisionLevelName   string          `json:"supervisionLevelName"`   // 監督級別名稱
    BenefitsName           string          `json:"benefitsName"`           // 福利名稱
    MinHourlyRate          decimal.Decimal `json:"minHourlyRate"`          // 最低時薪
    MaxHourlyRate          decimal.Decimal `json:"maxHourlyRate"`          // 最高時薪
    
    // 新增中優先級字段
    AcceptedCount          int32           `json:"acceptedCount"`          // 已取錄人數
    ApplicantCount         int32           `json:"applicantCount"`         // 申請人數
    Progress               string          `json:"progress"`               // 進度狀態
    JobCategory            string          `json:"jobCategory"`            // 工作分類
    PublishTime            *time.Time      `json:"publishTime"`            // 發佈時間
    PublishNow             string          `json:"publishNow"`             // 是否立即發佈
}
```

### 方案 2：僅添加核心字段
如果不想添加太多字段，建議至少添加以下核心字段：
1. `FacilityName` - 機構名稱
2. `JobNo` - 工作編號
3. `PositionProfessionName` - 職位專業名稱
4. `MinHourlyRate` / `MaxHourlyRate` - 薪資範圍
5. 其他 `*Name` 字段 - 用於前端顯示

## 測試建議
添加字段後，建議：
1. 更新相關的查詢邏輯
2. 確保前端能正確顯示新字段
3. 添加單元測試驗證新字段的正確性
4. 檢查是否需要更新 API 文檔

## 結論
JobScheduleInquireResp 缺少的主要是顯示名稱字段、薪資信息和統計字段。建議優先添加高優先級字段，以提升用戶體驗和功能完整性。
